# Interfacing with the YogaBot Live Application

This document outlines the public endpoints and URLs provided by the main application (`app.yogabot.live`) for use by the marketing website.

## Public API Endpoints

### `GET /api/public/plans`

*   **Purpose:** Fetches all active subscription plans to be displayed on the pricing page.
*   **Method:** `GET`
*   **URL:** `https://app.yogabot.live/api/public/plans`
*   **Authentication:** None required (public endpoint)
*   **CORS:** Enabled for all origins (configure for production)
*   **Response:** A JSON array of plan objects.

```json
[
  {
    "id": "plan_id_123",
    "name": "Basic",
    "price": 2900,
    "features": {
      "chatbotLimit": 1,
      "tokenLimit": 25000,
      "kbType": "simple",
      "canUseBYOK": false
    }
  },
  {
    "id": "plan_id_456",
    "name": "Pro",
    "price": 4900,
    "features": {
      "chatbotLimit": 3,
      "tokenLimit": 50000,
      "kbType": "structured",
      "canUseBYOK": true
    }
  }
]
```

**Response Fields:**
- `id`: Unique plan identifier (use this for signup links)
- `name`: Display name of the plan
- `price`: Price in cents (divide by 100 for display)
- `features`: Object containing plan capabilities
  - `chatbotLimit`: Number of chatbots allowed
  - `tokenLimit`: Monthly token allowance
  - `kbType`: Knowledge base type ("simple" or "structured")
  - `canUseBYOK`: Whether user can bring their own API keys

## User Entry Point URLs

### Login URL
To send existing users to log in, link to:
```
https://app.yogabot.live/login
```

### Signup & Subscribe URL
To send new users to sign up for a specific plan, link to:
```
https://app.yogabot.live/signup?planId=<PLAN_ID_FROM_API>
```

Replace `<PLAN_ID_FROM_API>` with the actual `id` of the chosen plan from the `/api/public/plans` response.

**Example:**
```html
<a href="https://app.yogabot.live/signup?planId=plan_id_456">
  Get Started with Pro Plan
</a>
```

## Implementation Examples

### JavaScript/React Example

```javascript
// Fetch plans for pricing page
async function fetchPlans() {
  try {
    const response = await fetch('https://app.yogabot.live/api/public/plans');
    const plans = await response.json();
    
    return plans.map(plan => ({
      ...plan,
      displayPrice: `₹${(plan.price / 100).toLocaleString()}/month`,
      signupUrl: `https://app.yogabot.live/signup?planId=${plan.id}`
    }));
  } catch (error) {
    console.error('Failed to fetch plans:', error);
    return [];
  }
}

// Usage in component
const plans = await fetchPlans();
```

### HTML Example

```html
<!-- Pricing section -->
<div class="pricing-plans">
  <!-- This would be populated dynamically from the API -->
  <div class="plan-card">
    <h3>Pro Plan</h3>
    <p class="price">₹4,900/month</p>
    <ul class="features">
      <li>3 Chatbots</li>
      <li>50,000 Tokens/month</li>
      <li>Advanced Knowledge Base</li>
      <li>Bring Your Own Keys</li>
    </ul>
    <a href="https://app.yogabot.live/signup?planId=plan_id_456" 
       class="cta-button">
      Get Started
    </a>
  </div>
</div>
```

## Error Handling

The API may return errors in the following format:

```json
{
  "error": "Error message description"
}
```

Common HTTP status codes:
- `200`: Success
- `500`: Server error (retry after some time)

## CORS Configuration

The public API endpoints are configured with CORS headers to allow cross-origin requests. In production, these should be restricted to your marketing website domain for security.

## Rate Limiting

The public endpoints have basic rate limiting applied. For high-traffic marketing sites, consider caching the plans data and refreshing it periodically rather than fetching on every page load.

## Support

For technical integration support or questions about the API, contact the development team.

---

*Last updated: June 19, 2025*
