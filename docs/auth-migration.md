# Authentication Middleware Migration Guide

## Overview
This document explains the new standardized authentication system implemented to replace inconsistent auth patterns across the codebase.

## New System Components

### 1. Enhanced Authentication (`/src/lib/enhanced-auth.ts`)
- `verifyUserWithRateLimit()` - User auth with built-in rate limiting
- `verifyAdminWithRateLimit()` - Admin auth with built-in rate limiting  
- `validateResourceOwnership()` - Ensures users can only access their resources
- `handleRouteError()` - Standardized error handling
- `RateLimits` - Pre-configured rate limit settings

### 2. Core Middleware (`/src/lib/auth-middleware.ts`)
- `withAuth()` - Full featured auth middleware
- `withUserAuth()` - Convenience wrapper for user routes
- `withAdminAuth()` - Convenience wrapper for admin routes

## Migration Patterns

### Before (Inconsistent):
```typescript
// Pattern 1: Manual session checking
const session = await getServerSession(authOptions)
if (!session?.user?.id) {
  return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
}

// Pattern 2: Helper functions (good but no rate limiting)
const { user } = await verifyUser()

// Pattern 3: Admin verification
await verifyAdmin()
```

### After (Standardized):
```typescript
// Enhanced helper functions with rate limiting
const { user } = await verifyUserWithRateLimit(request, RateLimits.NORMAL)

// With resource ownership validation
await validateResourceOwnership(user.id, chatbotId, 'chatbot')

// Standardized error handling
try {
  // route logic
} catch (error) {
  return handleRouteError(error)
}
```

## Benefits
- ✅ Consistent authentication patterns
- ✅ Built-in rate limiting on all routes
- ✅ Resource ownership validation
- ✅ Standardized error responses
- ✅ Better TypeScript support
- ✅ Centralized security logic

## Rate Limiting Configurations
- `RateLimits.STRICT`: 10 requests/minute (sensitive operations)
- `RateLimits.NORMAL`: 100 requests/hour (standard API)
- `RateLimits.RELAXED`: 1000 requests/hour (bulk operations)
- `RateLimits.PUBLIC`: 50 requests/minute (public endpoints)

## Migration Status
- [x] Core middleware implemented
- [x] Enhanced auth functions implemented
- [x] User routes migration (3 routes completed)
  - [x] `/api/user/chatbots/[chatbotId]/route.ts` - Complete with ownership validation
  - [ ] `/api/user/chatbots/[chatbotId]/assign-kb/route.ts` - Pending
  - [ ] `/api/user/knowledge-bases/[id]/route.ts` - Pending
  - [ ] `/api/user/knowledge-bases/route.ts` - Pending
  - [ ] Additional user routes - Pending
- [x] Admin routes migration (1 route completed)
  - [x] `/api/admin/chatbots/[chatbotId]/route.ts` - Enhanced with rate limiting
  - [ ] `/api/admin/users/[id]/route.ts` - Pending
  - [ ] `/api/admin/plans/[planId]/route.ts` - Pending
  - [ ] Additional admin routes - Pending
- [x] Public routes rate limiting (1 route completed)
  - [x] `/api/chat/send/route.ts` - Rate limiting implemented
  - [ ] `/api/chat/send-human/route.ts` - Pending
  - [ ] `/api/widget/[botId]` - Pending (if needed)

## Next Priority Routes
1. **User Routes**: Knowledge base management routes
2. **Admin Routes**: User and plan management routes  
3. **Public Routes**: Remaining chat endpoints
