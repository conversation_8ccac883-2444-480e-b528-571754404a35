// This is your Prisma schema file.

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("POSTGRES_PRISMA_URL")
}

// ===================================
// 1. Core SaaS & User Models
// ===================================

model User {
  id                        String          @id @default(cuid())
  email                     String          @unique
  name                      String?
  password                  String          // Stores a secure hash from bcrypt
  role                      Role            @default(USER)
  chatbots                  Chatbot[]       // A user can now have multiple chatbots
  knowledgeBases            KnowledgeBase[] // A user can have multiple knowledge bases
  subscription              Subscription?

  // Admin-controlled User Settings
  canCustomizeSystemPrompt  Boolean         @default(false) // Admin can enable this for specific users
  customSystemPrompt        String?         @db.Text        // User's custom system prompt (if enabled)
  useCustomSystemPrompt     Boolean         @default(false) // Whether user wants to use custom prompt
  simpleKbCharacterLimit    Int?                            // Admin override for user's simple KB character limit

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

enum Role {
  ADMIN
  USER
}

model Plan {
  id        String   @id @default(cuid())
  name      String   @unique // e.g., "Basic", "Pro", "Enterprise"
  price     Int      // Price in cents
  features  Json     // Defines capabilities: { chatbotLimit: 1, tokenLimit: 50000, kbType: "simple", canUseBYOK: false, ... }
  isActive  Boolean  @default(true)

  subscriptions Subscription[]
}

model Subscription {
  id                     String    @id @default(cuid())
  userId                 String    @unique
  user                   User      @relation(fields: [userId], references: [id])
  planId                 String
  plan                   Plan      @relation(fields: [planId], references: [id])
  status                 String    // e.g., "active", "cancelled", "past_due"
  currentPeriodEnd       DateTime
  razorpaySubscriptionId String?   @unique

  // Usage Tracking
  tokensUsedThisPeriod   Int       @default(0)
  sessionsThisPeriod     Int       @default(0)
}

// ===================================
// Global System Configuration
// ===================================

model SystemConfig {
  id                    String    @id @default(cuid())
  key                   String    @unique // e.g., "default_system_prompt"
  value                 String    @db.Text
  description           String?   @db.Text

  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt
}

// ===================================
// 2. Chatbot & Prompt Configuration
// ===================================

model Chatbot {
  id                    String    @id @default(cuid())
  userId                String
  user                  User      @relation(fields: [userId], references: [id])
  approvedDomain        String

  // Admin-controlled LLM settings & Overrides
  llmProvider           String    @default("gemini")
  llmModel              String    @default("gemini-1.5-flash")
  encryptedLlmApiKey    String?   // For BYOK (Bring Your Own Key)
  kbTypeOverride        String?   // Admin override: "simple" or "structured"

  // User-controlled settings
  widgetConfig          Json?
  smtpConfig            Json?     // Encrypted SMTP credentials
  personas              Persona[]

  // Knowledge Base Assignment (One-to-One relationship)
  assignedKbId          String?   @unique
  assignedKb            KnowledgeBase? @relation(fields: [assignedKbId], references: [id])

  // Legacy KB Data (for backward compatibility - will be migrated)
  simpleKbText          String?   @db.Text // For "simple" plan users
  structuredKbBrand     SchoolBrand?
  structuredKbContact   SchoolContact?
  structuredKbTeachers  Teacher[]
  structuredKbTtcs      TTC[]
  structuredKbRetreats  Retreat[]
  structuredKbPolicies  Policy[]
  structuredKbFaqs      FAQ[]

  // Searchable Data & History
  knowledgeChunks       KnowledgeBaseChunk[]
  chatSessions          ChatSession[]
}

model KnowledgeBase {
  id                    String    @id @default(cuid())
  userId                String
  user                  User      @relation(fields: [userId], references: [id])
  name                  String    // User-defined name for the KB
  description           String?   @db.Text
  kbType                String    // "simple" or "structured"

  // Simple KB Data
  simpleKbText          String?   @db.Text

  // Structured KB Data
  structuredKbBrand     KBSchoolBrand?
  structuredKbContact   KBSchoolContact?
  structuredKbTeachers  KBTeacher[]
  structuredKbTtcs      KBTTC[]
  structuredKbRetreats  KBRetreat[]
  structuredKbPolicies  KBPolicy[]
  structuredKbFaqs      KBFAQ[]

  // Processing status
  needsReprocessing     Boolean   @default(false) // True when content changed and needs re-embedding
  lastProcessedAt       DateTime?                  // When chunks were last generated

  // Searchable Data
  knowledgeChunks       KBChunk[]

  // Assignment
  assignedChatbot       Chatbot?

  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt
}

model Persona {
  id          String   @id @default(cuid())
  chatbotId   String
  chatbot     Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  name        String
  personaText String   @db.Text
  isActive    Boolean  @default(true)
}

// Legacy model for backward compatibility - will be migrated
model KnowledgeBaseChunk {
  id                 String   @id @default(cuid())
  chatbotId          String
  chatbot            Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  content            String   @db.Text
  content_tsvector   Unsupported("tsvector")? // Special column for Full-Text Search

  source             String?  // e.g., "TTC: 200-Hour Foundational" to identify the source

  @@index([content_tsvector], map: "content_tsvector_idx", type: Gin) // CRITICAL for FTS performance
}

// New KB-specific chunk model
model KBChunk {
  id                 String         @id @default(cuid())
  knowledgeBaseId    String
  knowledgeBase      KnowledgeBase  @relation(fields: [knowledgeBaseId], references: [id], onDelete: Cascade)
  content            String         @db.Text
  content_tsvector   Unsupported("tsvector")? // Special column for Full-Text Search

  source             String?        // e.g., "TTC: 200-Hour Foundational" to identify the source

  @@index([content_tsvector], map: "kb_content_tsvector_idx", type: Gin) // CRITICAL for FTS performance
}

// ===================================
// 3. Structured Knowledge Base Models
// (Unchanged from previous versions)
// ===================================

model SchoolBrand {
  id              String   @id @default(cuid())
  chatbotId       String   @unique
  chatbot         Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  schoolName      String
  tagline         String?
  schoolType      String?
  yogaStylesTaught String[]
  missionStatement String?   @db.Text
  aboutTheSchool  String?   @db.Text
  founderInfo     String?   @db.Text
}

model SchoolContact {
  id              String   @id @default(cuid())
  chatbotId       String   @unique
  chatbot         Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  fullAddress     String?
  googleMapsLink  String?
  howToReach      String?   @db.Text
  primaryPhone    String?
  whatsappNumber  String?
  primaryEmail    String?
  websiteUrl      String?
  socialMediaLinks Json?     // [{ platform: 'Instagram', url: '...' }]
}

model Teacher {
  id              String   @id @default(cuid())
  chatbotId       String
  chatbot         Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  name            String
  role            String?
  photoUrl        String?
  bio             String?   @db.Text
  certifications  String[]
}

model TTC {
  id                  String   @id @default(cuid())
  chatbotId           String
  chatbot             Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  name                String
  certificationBody   String?
  summary             String?   @db.Text
  duration            String?
  skillLevel          String?
  curriculumDetails   String?   @db.Text
  sampleDailySchedule String?   @db.Text
  priceOptions        Json     // [{ type: 'Shared Twin', price: 1800 }, ...]
  inclusions          String[]
  exclusions          String[]
  upcomingDates       Json     // [{ start: '...', end: '...', status: 'Open' }]
  applicationProcess  String?   @db.Text
}

model Retreat {
  id                  String   @id @default(cuid())
  chatbotId           String
  chatbot             Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  name                String
  theme               String?
  duration            String?
  intendedAudience    String?
  highlights          String[]
  priceOptions        Json
  upcomingDates       Json
}

model Policy {
  id                            String   @id @default(cuid())
  chatbotId                     String   @unique
  chatbot                       Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  codeOfConduct                 String?   @db.Text
  paymentPolicy                 String?   @db.Text
  cancellationAndRefundPolicy   String?   @db.Text
}

model FAQ {
  id          String   @id @default(cuid())
  chatbotId   String
  chatbot     Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  question    String
  answer      String   @db.Text
}

// ===================================
// 5. New KB-Specific Structured Models
// ===================================

model KBSchoolBrand {
  id              String         @id @default(cuid())
  knowledgeBaseId String         @unique
  knowledgeBase   KnowledgeBase  @relation(fields: [knowledgeBaseId], references: [id], onDelete: Cascade)
  schoolName      String
  tagline         String?
  schoolType      String?
  yogaStylesTaught String[]
  missionStatement String?        @db.Text
  aboutTheSchool  String?        @db.Text
  founderInfo     String?        @db.Text
}

model KBSchoolContact {
  id              String         @id @default(cuid())
  knowledgeBaseId String         @unique
  knowledgeBase   KnowledgeBase  @relation(fields: [knowledgeBaseId], references: [id], onDelete: Cascade)
  fullAddress     String?
  googleMapsLink  String?
  howToReach      String?        @db.Text
  primaryPhone    String?
  whatsappNumber  String?
  primaryEmail    String?
  websiteUrl      String?
  socialMediaLinks Json?          // [{ platform: 'Instagram', url: '...' }]
}

model KBTeacher {
  id              String         @id @default(cuid())
  knowledgeBaseId String
  knowledgeBase   KnowledgeBase  @relation(fields: [knowledgeBaseId], references: [id], onDelete: Cascade)
  name            String
  role            String?
  photoUrl        String?
  bio             String?        @db.Text
  certifications  String[]
}

model KBTTC {
  id                  String         @id @default(cuid())
  knowledgeBaseId     String
  knowledgeBase       KnowledgeBase  @relation(fields: [knowledgeBaseId], references: [id], onDelete: Cascade)
  name                String
  certificationBody   String?
  summary             String?        @db.Text
  duration            String?
  skillLevel          String?
  curriculumDetails   String?        @db.Text
  sampleDailySchedule String?        @db.Text
  priceOptions        Json           // [{ type: 'Shared Twin', price: 1800 }, ...]
  inclusions          String[]
  exclusions          String[]
  upcomingDates       Json           // [{ start: '...', end: '...', status: 'Open' }]
  applicationProcess  String?        @db.Text
}

model KBRetreat {
  id                  String         @id @default(cuid())
  knowledgeBaseId     String
  knowledgeBase       KnowledgeBase  @relation(fields: [knowledgeBaseId], references: [id], onDelete: Cascade)
  name                String
  theme               String?
  duration            String?
  intendedAudience    String?
  highlights          String[]
  priceOptions        Json
  upcomingDates       Json
}

model KBPolicy {
  id                            String         @id @default(cuid())
  knowledgeBaseId               String         @unique
  knowledgeBase                 KnowledgeBase  @relation(fields: [knowledgeBaseId], references: [id], onDelete: Cascade)
  codeOfConduct                 String?        @db.Text
  paymentPolicy                 String?        @db.Text
  cancellationAndRefundPolicy   String?        @db.Text
}

model KBFAQ {
  id              String         @id @default(cuid())
  knowledgeBaseId String
  knowledgeBase   KnowledgeBase  @relation(fields: [knowledgeBaseId], references: [id], onDelete: Cascade)
  question        String
  answer          String         @db.Text
}


// ===================================
// 4. Chat History & Visitor Models
// (Unchanged from previous versions)
// ===================================

model ChatSession {
  id            String    @id @default(cuid())
  chatbotId     String
  chatbot       Chatbot   @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  visitorId     String
  visitor       Visitor   @relation(fields: [visitorId], references: [id])
  controller    String    @default("LLM") // Can be "LLM" or "USER"
  ablyChannel   String    @unique
  tokenCount    Int       @default(0)

  messages      Message[]
  createdAt     DateTime  @default(now())
}

model Message {
  id            String      @id @default(cuid())
  chatSessionId String
  chatSession   ChatSession @relation(fields: [chatSessionId], references: [id], onDelete: Cascade)
  senderType    String      // "VISITOR", "LLM", or "USER"
  content       String      @db.Text
  systemData    Json?       // For debugging: stores raw LLM tool calls, prompts, etc.
  createdAt     DateTime    @default(now())
}

model Visitor {
  id           String        @id @default(cuid())
  email        String?
  name         String?
  profileData  Json?
  chatSessions ChatSession[]
}
