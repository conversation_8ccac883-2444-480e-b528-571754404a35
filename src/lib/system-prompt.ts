import { prisma } from "./prisma"

/**
 * Get the effective system prompt for a chatbot
 * This function determines whether to use the user's custom system prompt
 * or the global default system prompt based on the user's configuration
 */
export async function getEffectiveSystemPrompt(chatbotId: string): Promise<string> {
  try {
    // Get chatbot with user permissions and settings
    const chatbot = await prisma.chatbot.findUnique({
      where: { id: chatbotId },
      include: {
        user: {
          select: {
            canCustomizeSystemPrompt: true,
            useCustomSystemPrompt: true,
            customSystemPrompt: true
          }
        }
      }
    })

    if (!chatbot) {
      throw new Error("Chatbot not found")
    }

    // If user has permission and wants to use custom prompt, and has provided one
    if (
      chatbot.user.canCustomizeSystemPrompt &&
      chatbot.user.useCustomSystemPrompt &&
      chatbot.user.customSystemPrompt?.trim()
    ) {
      return chatbot.user.customSystemPrompt
    }

    // Otherwise, use the global default system prompt
    const defaultConfig = await prisma.systemConfig.findUnique({
      where: { key: 'default_system_prompt' }
    })

    if (!defaultConfig) {
      // Fallback system prompt if no configuration exists
      return `You are a helpful and knowledgeable assistant for a yoga school or wellness center. Your role is to provide accurate information about classes, teachers, schedules, policies, and general yoga-related questions.

Guidelines:
- Be warm, welcoming, and professional in your responses
- Provide accurate information based on the knowledge base provided
- If you don't have specific information, politely direct users to contact the school directly
- Focus on being helpful while maintaining the peaceful and mindful spirit of yoga
- Use clear, easy-to-understand language
- Be encouraging and supportive of people's yoga journey

Always prioritize accuracy and helpfulness in your responses.`
    }

    return defaultConfig.value
  } catch (error) {
    console.error("Error getting effective system prompt:", error)
    // Return fallback prompt on error
    return `You are a helpful and knowledgeable assistant for a yoga school or wellness center. Your role is to provide accurate information about classes, teachers, schedules, policies, and general yoga-related questions.

Guidelines:
- Be warm, welcoming, and professional in your responses
- Provide accurate information based on the knowledge base provided
- If you don't have specific information, politely direct users to contact the school directly
- Focus on being helpful while maintaining the peaceful and mindful spirit of yoga
- Use clear, easy-to-understand language
- Be encouraging and supportive of people's yoga journey

Always prioritize accuracy and helpfulness in your responses.`
  }
}

/**
 * Get the global default system prompt
 */
export async function getDefaultSystemPrompt(): Promise<string> {
  try {
    const defaultConfig = await prisma.systemConfig.findUnique({
      where: { key: 'default_system_prompt' }
    })

    return defaultConfig?.value || "You are a helpful assistant."
  } catch (error) {
    console.error("Error getting default system prompt:", error)
    return "You are a helpful assistant."
  }
}

/**
 * Update the global default system prompt (admin only)
 */
export async function updateDefaultSystemPrompt(newPrompt: string): Promise<void> {
  try {
    await prisma.systemConfig.upsert({
      where: { key: 'default_system_prompt' },
      update: { value: newPrompt },
      create: {
        key: 'default_system_prompt',
        value: newPrompt,
        description: 'Default system prompt used by all chatbots unless overridden by users with custom prompt permissions'
      }
    })
  } catch (error) {
    console.error("Error updating default system prompt:", error)
    throw error
  }
}
