import { NextRequest, NextResponse } from 'next/server'
import { withUser<PERSON><PERSON>, withAdminAuth, RateLimits } from './auth-middleware'

// Legacy compatibility wrapper
export function createUserRoute(
  handler: (request: NextRequest, context: { params: any, user: any }) => Promise<NextResponse>
) {
  return withUserAuth(handler, {
    rateLimit: RateLimits.NORMAL
  })
}

export function createAdminRoute(
  handler: (request: NextRequest, context: { params: any, user: any }) => Promise<NextResponse>
) {
  return withAdminAuth(handler, {
    rateLimit: RateLimits.NORMAL
  })
}

export function createUserRouteWithOwnership(
  handler: (request: NextRequest, context: { params: any, user: any }) => Promise<NextResponse>,
  resourceType: 'chatbot' | 'knowledgeBase',
  paramName: string
) {
  return withUserAuth(handler, {
    rateLimit: RateLimits.NORMAL,
    validateOwnership: {
      resourceType,
      paramName
    }
  })
}
