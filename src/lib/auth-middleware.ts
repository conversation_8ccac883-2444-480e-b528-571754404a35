import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// Rate limiting store (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

export interface AuthOptions {
  requireRole?: 'USER' | 'ADMIN' | 'ANY'
  requireAuth?: boolean
  rateLimit?: {
    requests: number
    window: string // e.g., '1h', '1m', '1d'
  }
  validateOwnership?: {
    resourceType: 'chatbot' | 'knowledgeBase' | 'user'
    paramName: string // e.g., 'chatbotId', 'id'
  }
  allowPublic?: boolean
}

export interface AuthenticatedRequest extends NextRequest {
  user?: {
    id: string
    email: string
    role: string
  }
}

export interface RouteContext {
  params?: Record<string, string>
  user?: {
    id: string
    email: string
    role: string
  }
}

type RouteHandler = (
  request: AuthenticatedRequest, 
  context: RouteContext
) => Promise<NextResponse> | NextResponse

// Parse time window to milliseconds
function parseTimeWindow(window: string): number {
  const match = window.match(/^(\d+)([hmsd])$/)
  if (!match) throw new Error(`Invalid time window format: ${window}`)
  
  const [, amount, unit] = match
  const multipliers = {
    s: 1000,
    m: 60 * 1000,
    h: 60 * 60 * 1000,
    d: 24 * 60 * 60 * 1000
  }
  
  return parseInt(amount) * multipliers[unit as keyof typeof multipliers]
}

// Apply rate limiting
async function applyRateLimit(
  request: NextRequest, 
  options: NonNullable<AuthOptions['rateLimit']>
): Promise<void> {
  const clientIP = request.headers.get('x-forwarded-for')?.split(',')[0] || 
    request.headers.get('x-real-ip') || 
    'unknown'
  
  const key = `rate_limit:${clientIP}`
  const now = Date.now()
  const windowMs = parseTimeWindow(options.window)
  
  const existing = rateLimitStore.get(key)
  
  if (!existing || now > existing.resetTime) {
    // First request or window expired
    rateLimitStore.set(key, {
      count: 1,
      resetTime: now + windowMs
    })
    return
  }
  
  if (existing.count >= options.requests) {
    throw new Error(`Rate limit exceeded. Max ${options.requests} requests per ${options.window}`)
  }
  
  existing.count++
  rateLimitStore.set(key, existing)
}

// Validate resource ownership
async function validateResourceOwnership(
  userId: string,
  resourceId: string,
  resourceType: NonNullable<AuthOptions['validateOwnership']>['resourceType']
): Promise<void> {
  let resource: { id: string } | null = null
  
  switch (resourceType) {
    case 'chatbot':
      resource = await prisma.chatbot.findFirst({
        where: { id: resourceId, userId },
        select: { id: true }
      })
      break
      
    case 'knowledgeBase':
      resource = await prisma.knowledgeBase.findFirst({
        where: { id: resourceId, userId },
        select: { id: true }
      })
      break
      
    case 'user':
      if (resourceId !== userId) {
        throw new Error('Access denied: Resource ownership validation failed')
      }
      return
      
    default:
      throw new Error(`Unknown resource type: ${resourceType}`)
  }
  
  if (!resource) {
    throw new Error('Access denied: Resource not found or access denied')
  }
}

// Create standardized error responses
function createErrorResponse(message: string, status: number): NextResponse {
  return NextResponse.json(
    { 
      error: message,
      timestamp: new Date().toISOString()
    }, 
    { status }
  )
}

// Main authentication middleware
export function withAuth(
  handler: RouteHandler,
  options: AuthOptions = {}
): RouteHandler {
  const {
    requireRole = 'USER',
    requireAuth = true,
    rateLimit,
    validateOwnership,
    allowPublic = false
  } = options

  return async (request: AuthenticatedRequest, context: RouteContext) => {
    try {
      // 1. Apply rate limiting if configured
      if (rateLimit) {
        await applyRateLimit(request, rateLimit)
      }

      // 2. Handle public routes
      if (allowPublic && !requireAuth) {
        return handler(request, context)
      }

      // 3. Get and validate session
      const session = await getServerSession(authOptions)
      
      if (!session?.user) {
        return createErrorResponse('Authentication required', 401)
      }

      // 4. Validate role requirements
      if (requireRole !== 'ANY') {
        if (requireRole === 'ADMIN' && session.user.role !== 'ADMIN') {
          return createErrorResponse('Admin access required', 403)
        }
        if (requireRole === 'USER' && !session.user.role) {
          return createErrorResponse('User access required', 403)
        }
      }

      // 5. Validate resource ownership if configured
      if (validateOwnership && context.params) {
        const resourceId = context.params[validateOwnership.paramName]
        if (resourceId) {
          await validateResourceOwnership(
            session.user.id, 
            resourceId, 
            validateOwnership.resourceType
          )
        }
      }

      // 6. Attach user to request and context
      request.user = session.user
      context.user = session.user

      // 7. Execute the handler
      return handler(request, context)

    } catch (error) {
      console.error('Auth middleware error:', error)
      
      if (error instanceof Error) {
        if (error.message.includes('Rate limit exceeded')) {
          return createErrorResponse(error.message, 429)
        }
        if (error.message.includes('Access denied')) {
          return createErrorResponse(error.message, 403)
        }
      }
      
      return createErrorResponse('Internal server error', 500)
    }
  }
}

// Convenience functions for common auth patterns
export const withUserAuth = (handler: RouteHandler, options: Omit<AuthOptions, 'requireRole'> = {}) =>
  withAuth(handler, { ...options, requireRole: 'USER' })

export const withAdminAuth = (handler: RouteHandler, options: Omit<AuthOptions, 'requireRole'> = {}) =>
  withAuth(handler, { ...options, requireRole: 'ADMIN' })

export const withPublicAccess = (handler: RouteHandler, options: Omit<AuthOptions, 'allowPublic' | 'requireAuth'> = {}) =>
  withAuth(handler, { ...options, allowPublic: true, requireAuth: false })

// Rate limiting configurations
export const RateLimits = {
  STRICT: { requests: 10, window: '1m' },
  NORMAL: { requests: 100, window: '1h' },
  RELAXED: { requests: 1000, window: '1h' },
  PUBLIC: { requests: 50, window: '1m' }
} as const
