import { prisma } from './prisma';

// ===================================
// Tool Functions - Server-side functions the LLM can call
// ===================================

/**
 * Get all upcoming Teacher Training Courses (TTCs) for a chatbot
 */
export async function getUpcomingTTCs(chatbotId: string) {
  try {
    // First check if chatbot has assigned KB
    const chatbot = await prisma.chatbot.findUnique({
      where: { id: chatbotId },
      include: { assignedKb: true }
    });

    if (!chatbot) {
      return { error: "Chatbot not found" };
    }

    let ttcs = [];

    // Check new KB structure first
    if (chatbot.assignedKb) {
      ttcs = await prisma.kBTTC.findMany({
        where: { knowledgeBaseId: chatbot.assignedKb.id }
      });
    } else {
      // Fallback to legacy structure
      ttcs = await prisma.tTC.findMany({
        where: { chatbotId }
      });
    }

    // Filter for upcoming dates
    const now = new Date();
    const upcomingTTCs = ttcs.filter(ttc => {
      if (!ttc.upcomingDates || !Array.isArray(ttc.upcomingDates)) return false;
      
      return ttc.upcomingDates.some((date: any) => {
        if (!date.start) return false;
        const startDate = new Date(date.start);
        return startDate > now && date.status !== 'Closed';
      });
    });

    return {
      success: true,
      data: upcomingTTCs.map(ttc => ({
        name: ttc.name,
        duration: ttc.duration,
        skillLevel: ttc.skillLevel,
        summary: ttc.summary,
        priceOptions: ttc.priceOptions,
        upcomingDates: ttc.upcomingDates,
        inclusions: ttc.inclusions,
        exclusions: ttc.exclusions,
        applicationProcess: ttc.applicationProcess
      }))
    };
  } catch (error) {
    console.error('Error fetching upcoming TTCs:', error);
    return { error: "Failed to fetch upcoming TTCs" };
  }
}

/**
 * Get detailed information about a specific teacher by name
 */
export async function getTeacherBio(chatbotId: string, teacherName: string) {
  try {
    const chatbot = await prisma.chatbot.findUnique({
      where: { id: chatbotId },
      include: { assignedKb: true }
    });

    if (!chatbot) {
      return { error: "Chatbot not found" };
    }

    let teacher = null;

    // Check new KB structure first
    if (chatbot.assignedKb) {
      teacher = await prisma.kBTeacher.findFirst({
        where: { 
          knowledgeBaseId: chatbot.assignedKb.id,
          name: { contains: teacherName, mode: 'insensitive' }
        }
      });
    } else {
      // Fallback to legacy structure
      teacher = await prisma.teacher.findFirst({
        where: { 
          chatbotId,
          name: { contains: teacherName, mode: 'insensitive' }
        }
      });
    }

    if (!teacher) {
      return { error: `Teacher "${teacherName}" not found` };
    }

    return {
      success: true,
      data: {
        name: teacher.name,
        role: teacher.role,
        bio: teacher.bio,
        certifications: teacher.certifications,
        photoUrl: teacher.photoUrl
      }
    };
  } catch (error) {
    console.error('Error fetching teacher bio:', error);
    return { error: "Failed to fetch teacher information" };
  }
}

/**
 * Get all available retreats for a chatbot
 */
export async function getAvailableRetreats(chatbotId: string) {
  try {
    const chatbot = await prisma.chatbot.findUnique({
      where: { id: chatbotId },
      include: { assignedKb: true }
    });

    if (!chatbot) {
      return { error: "Chatbot not found" };
    }

    let retreats = [];

    // Check new KB structure first
    if (chatbot.assignedKb) {
      retreats = await prisma.kBRetreat.findMany({
        where: { knowledgeBaseId: chatbot.assignedKb.id }
      });
    } else {
      // Fallback to legacy structure
      retreats = await prisma.retreat.findMany({
        where: { chatbotId }
      });
    }

    // Filter for upcoming dates
    const now = new Date();
    const availableRetreats = retreats.filter(retreat => {
      if (!retreat.upcomingDates || !Array.isArray(retreat.upcomingDates)) return false;
      
      return retreat.upcomingDates.some((date: any) => {
        if (!date.start) return false;
        const startDate = new Date(date.start);
        return startDate > now && date.status !== 'Closed';
      });
    });

    return {
      success: true,
      data: availableRetreats.map(retreat => ({
        name: retreat.name,
        theme: retreat.theme,
        duration: retreat.duration,
        intendedAudience: retreat.intendedAudience,
        highlights: retreat.highlights,
        priceOptions: retreat.priceOptions,
        upcomingDates: retreat.upcomingDates
      }))
    };
  } catch (error) {
    console.error('Error fetching available retreats:', error);
    return { error: "Failed to fetch retreat information" };
  }
}

/**
 * Get school contact information
 */
export async function getSchoolContact(chatbotId: string) {
  try {
    const chatbot = await prisma.chatbot.findUnique({
      where: { id: chatbotId },
      include: { assignedKb: true }
    });

    if (!chatbot) {
      return { error: "Chatbot not found" };
    }

    let contact = null;

    // Check new KB structure first
    if (chatbot.assignedKb) {
      contact = await prisma.kBSchoolContact.findUnique({
        where: { knowledgeBaseId: chatbot.assignedKb.id }
      });
    } else {
      // Fallback to legacy structure
      contact = await prisma.schoolContact.findUnique({
        where: { chatbotId }
      });
    }

    if (!contact) {
      return { error: "School contact information not found" };
    }

    return {
      success: true,
      data: {
        fullAddress: contact.fullAddress,
        googleMapsLink: contact.googleMapsLink,
        howToReach: contact.howToReach,
        primaryPhone: contact.primaryPhone,
        whatsappNumber: contact.whatsappNumber,
        primaryEmail: contact.primaryEmail,
        websiteUrl: contact.websiteUrl,
        socialMediaLinks: contact.socialMediaLinks
      }
    };
  } catch (error) {
    console.error('Error fetching school contact:', error);
    return { error: "Failed to fetch contact information" };
  }
}

/**
 * Get frequently asked questions
 */
export async function getFAQs(chatbotId: string, searchTerm?: string) {
  try {
    const chatbot = await prisma.chatbot.findUnique({
      where: { id: chatbotId },
      include: { assignedKb: true }
    });

    if (!chatbot) {
      return { error: "Chatbot not found" };
    }

    let faqs = [];

    // Check new KB structure first
    if (chatbot.assignedKb) {
      const whereClause: any = { knowledgeBaseId: chatbot.assignedKb.id };
      if (searchTerm) {
        whereClause.OR = [
          { question: { contains: searchTerm, mode: 'insensitive' } },
          { answer: { contains: searchTerm, mode: 'insensitive' } }
        ];
      }
      faqs = await prisma.kBFAQ.findMany({ where: whereClause });
    } else {
      // Fallback to legacy structure
      const whereClause: any = { chatbotId };
      if (searchTerm) {
        whereClause.OR = [
          { question: { contains: searchTerm, mode: 'insensitive' } },
          { answer: { contains: searchTerm, mode: 'insensitive' } }
        ];
      }
      faqs = await prisma.fAQ.findMany({ where: whereClause });
    }

    return {
      success: true,
      data: faqs.map(faq => ({
        question: faq.question,
        answer: faq.answer
      }))
    };
  } catch (error) {
    console.error('Error fetching FAQs:', error);
    return { error: "Failed to fetch FAQs" };
  }
}

/**
 * Get school policies (payment, cancellation, code of conduct)
 */
export async function getSchoolPolicies(chatbotId: string) {
  try {
    const chatbot = await prisma.chatbot.findUnique({
      where: { id: chatbotId },
      include: { assignedKb: true }
    });

    if (!chatbot) {
      return { error: "Chatbot not found" };
    }

    let policies = null;

    // Check new KB structure first
    if (chatbot.assignedKb) {
      policies = await prisma.kBPolicy.findUnique({
        where: { knowledgeBaseId: chatbot.assignedKb.id }
      });
    } else {
      // Fallback to legacy structure
      policies = await prisma.policy.findUnique({
        where: { chatbotId }
      });
    }

    if (!policies) {
      return { error: "School policies not found" };
    }

    return {
      success: true,
      data: {
        codeOfConduct: policies.codeOfConduct,
        paymentPolicy: policies.paymentPolicy,
        cancellationAndRefundPolicy: policies.cancellationAndRefundPolicy
      }
    };
  } catch (error) {
    console.error('Error fetching school policies:', error);
    return { error: "Failed to fetch school policies" };
  }
}

/**
 * Get all teachers for a chatbot
 */
export async function getAllTeachers(chatbotId: string) {
  try {
    const chatbot = await prisma.chatbot.findUnique({
      where: { id: chatbotId },
      include: { assignedKb: true }
    });

    if (!chatbot) {
      return { error: "Chatbot not found" };
    }

    let teachers = [];

    // Check new KB structure first
    if (chatbot.assignedKb) {
      teachers = await prisma.kBTeacher.findMany({
        where: { knowledgeBaseId: chatbot.assignedKb.id }
      });
    } else {
      // Fallback to legacy structure
      teachers = await prisma.teacher.findMany({
        where: { chatbotId }
      });
    }

    return {
      success: true,
      data: teachers.map(teacher => ({
        name: teacher.name,
        role: teacher.role,
        bio: teacher.bio,
        certifications: teacher.certifications,
        photoUrl: teacher.photoUrl
      }))
    };
  } catch (error) {
    console.error('Error fetching teachers:', error);
    return { error: "Failed to fetch teachers" };
  }
}

/**
 * Get school brand information
 */
export async function getSchoolBrand(chatbotId: string) {
  try {
    const chatbot = await prisma.chatbot.findUnique({
      where: { id: chatbotId },
      include: { assignedKb: true }
    });

    if (!chatbot) {
      return { error: "Chatbot not found" };
    }

    let brand = null;

    // Check new KB structure first
    if (chatbot.assignedKb) {
      brand = await prisma.kBSchoolBrand.findUnique({
        where: { knowledgeBaseId: chatbot.assignedKb.id }
      });
    } else {
      // Fallback to legacy structure
      brand = await prisma.schoolBrand.findUnique({
        where: { chatbotId }
      });
    }

    if (!brand) {
      return { error: "School brand information not found" };
    }

    return {
      success: true,
      data: {
        schoolName: brand.schoolName,
        tagline: brand.tagline,
        schoolType: brand.schoolType,
        yogaStylesTaught: brand.yogaStylesTaught,
        missionStatement: brand.missionStatement,
        aboutTheSchool: brand.aboutTheSchool,
        founderInfo: brand.founderInfo
      }
    };
  } catch (error) {
    console.error('Error fetching school brand:', error);
    return { error: "Failed to fetch school brand information" };
  }
}

// ===================================
// Tool Schemas - JSON schemas that define the tools for the LLM
// ===================================

import { SchemaType } from '@google/generative-ai';

export const toolSchemas = [
  {
    name: 'getUpcomingTTCs',
    description: 'Get a list of all upcoming Yoga Teacher Training Courses (TTCs) with their dates, prices, and availability.',
    parameters: {
      type: SchemaType.OBJECT,
      properties: {}
    }
  },
  {
    name: 'getTeacherBio',
    description: 'Get detailed biography and information for a specific teacher by name.',
    parameters: {
      type: SchemaType.OBJECT,
      properties: {
        teacherName: {
          type: SchemaType.STRING,
          description: 'The name of the teacher to look up'
        }
      },
      required: ['teacherName']
    }
  },
  {
    name: 'getAvailableRetreats',
    description: 'Get a list of all available yoga retreats with their themes, dates, and pricing.',
    parameters: {
      type: SchemaType.OBJECT,
      properties: {}
    }
  },
  {
    name: 'getSchoolContact',
    description: 'Get school contact information including address, phone numbers, email, and social media links.',
    parameters: {
      type: SchemaType.OBJECT,
      properties: {}
    }
  },
  {
    name: 'getFAQs',
    description: 'Get frequently asked questions, optionally filtered by a search term.',
    parameters: {
      type: SchemaType.OBJECT,
      properties: {
        searchTerm: {
          type: SchemaType.STRING,
          description: 'Optional search term to filter FAQs by question or answer content'
        }
      }
    }
  },
  {
    name: 'getSchoolPolicies',
    description: 'Get school policies including payment policy, cancellation/refund policy, and code of conduct.',
    parameters: {
      type: SchemaType.OBJECT,
      properties: {}
    }
  },
  {
    name: 'getAllTeachers',
    description: 'Get a list of all teachers with their roles, bios, and certifications.',
    parameters: {
      type: SchemaType.OBJECT,
      properties: {}
    }
  },
  {
    name: 'getSchoolBrand',
    description: 'Get school brand information including name, mission statement, yoga styles taught, and founder information.',
    parameters: {
      type: SchemaType.OBJECT,
      properties: {}
    }
  }
];

// ===================================
// Tool Executor - Maps tool names to functions
// ===================================

export const toolExecutor = {
  getUpcomingTTCs,
  getTeacherBio,
  getAvailableRetreats,
  getSchoolContact,
  getFAQs,
  getSchoolPolicies,
  getAllTeachers,
  getSchoolBrand
};

// Type for tool execution
export type ToolName = keyof typeof toolExecutor;
