import nodemailer from 'nodemailer'
import { render } from '@react-email/render'
import WelcomeEmail from '@/emails/WelcomeEmail'
import SubscriptionWarningEmail from '@/emails/SubscriptionWarningEmail'
import { safeDecrypt } from './encryption'

interface EmailConfig {
  host: string
  port: number
  secure: boolean
  user: string
  password: string
}

interface WelcomeEmailData {
  userName: string
  planName: string
  loginUrl: string
}

interface SubscriptionWarningEmailData {
  userName: string
  tokensUsed: number
  tokenLimit: number
  percentageUsed: number
  planName: string
  dashboardUrl: string
}

/**
 * Creates a nodemailer transporter from email configuration
 */
function createTransporter(emailConfig: EmailConfig) {
  return nodemailer.createTransporter({
    host: emailConfig.host,
    port: emailConfig.port,
    secure: emailConfig.secure,
    auth: {
      user: emailConfig.user,
      pass: emailConfig.password,
    },
  })
}

/**
 * Sends a welcome email to a new user
 */
export async function sendWelcomeEmail(
  to: string,
  data: WelcomeEmailData,
  emailConfig?: EmailConfig
): Promise<boolean> {
  try {
    // Use provided config or default system config
    const config = emailConfig || getSystemEmailConfig()
    if (!config) {
      console.error('No email configuration available')
      return false
    }

    const transporter = createTransporter(config)
    
    const emailHtml = render(WelcomeEmail(data))
    
    const mailOptions = {
      from: `"YogaBot Live" <${config.user}>`,
      to,
      subject: 'Welcome to YogaBot Live! 🧘‍♀️',
      html: emailHtml,
    }

    await transporter.sendMail(mailOptions)
    console.log(`Welcome email sent to ${to}`)
    return true

  } catch (error) {
    console.error('Error sending welcome email:', error)
    return false
  }
}

/**
 * Sends a subscription warning email
 */
export async function sendSubscriptionWarningEmail(
  to: string,
  data: SubscriptionWarningEmailData,
  emailConfig?: EmailConfig
): Promise<boolean> {
  try {
    // Use provided config or default system config
    const config = emailConfig || getSystemEmailConfig()
    if (!config) {
      console.error('No email configuration available')
      return false
    }

    const transporter = createTransporter(config)
    
    const emailHtml = render(SubscriptionWarningEmail(data))
    
    const mailOptions = {
      from: `"YogaBot Live" <${config.user}>`,
      to,
      subject: `Token Usage Alert - ${data.percentageUsed}% Used`,
      html: emailHtml,
    }

    await transporter.sendMail(mailOptions)
    console.log(`Subscription warning email sent to ${to}`)
    return true

  } catch (error) {
    console.error('Error sending subscription warning email:', error)
    return false
  }
}

/**
 * Sends a password reset email
 */
export async function sendPasswordResetEmail(
  to: string,
  resetToken: string,
  emailConfig?: EmailConfig
): Promise<boolean> {
  try {
    // Use provided config or default system config
    const config = emailConfig || getSystemEmailConfig()
    if (!config) {
      console.error('No email configuration available')
      return false
    }

    const transporter = createTransporter(config)
    const resetUrl = `${process.env.NEXTAUTH_URL}/reset-password?token=${resetToken}`
    
    const emailHtml = `
      <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
          <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <h1 style="color: #3B82F6;">Password Reset Request</h1>
            <p>You requested a password reset for your YogaBot Live account.</p>
            <p>Click the button below to reset your password:</p>
            <div style="text-align: center; margin: 30px 0;">
              <a href="${resetUrl}" style="background-color: #3B82F6; color: white; padding: 12px 30px; text-decoration: none; border-radius: 8px; font-weight: bold;">
                Reset Password
              </a>
            </div>
            <p>If you didn't request this reset, you can safely ignore this email.</p>
            <p>This link will expire in 1 hour for security reasons.</p>
            <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
            <p style="color: #666; font-size: 12px;">© 2025 YogaBot Live. All rights reserved.</p>
          </div>
        </body>
      </html>
    `
    
    const mailOptions = {
      from: `"YogaBot Live" <${config.user}>`,
      to,
      subject: 'Password Reset Request - YogaBot Live',
      html: emailHtml,
    }

    await transporter.sendMail(mailOptions)
    console.log(`Password reset email sent to ${to}`)
    return true

  } catch (error) {
    console.error('Error sending password reset email:', error)
    return false
  }
}

/**
 * Gets system email configuration from environment variables
 */
function getSystemEmailConfig(): EmailConfig | null {
  const host = process.env.SMTP_HOST
  const port = process.env.SMTP_PORT
  const user = process.env.SMTP_USER
  const password = process.env.SMTP_PASSWORD

  if (!host || !port || !user || !password) {
    return null
  }

  return {
    host,
    port: parseInt(port),
    secure: process.env.SMTP_SECURE === 'true',
    user,
    password,
  }
}

/**
 * Parses and decrypts SMTP configuration from chatbot settings
 */
export function parseSmtpConfig(smtpConfigData: any): EmailConfig | null {
  if (!smtpConfigData) return null

  try {
    let config = smtpConfigData

    // If password is encrypted, decrypt it
    if (typeof config.password === 'string' && config.password.includes(':')) {
      const decryptedPassword = safeDecrypt(config.password)
      if (decryptedPassword) {
        config = { ...config, password: decryptedPassword }
      }
    }

    // Validate required fields
    if (!config.host || !config.port || !config.user || !config.password) {
      return null
    }

    return {
      host: config.host,
      port: config.port,
      secure: config.secure || false,
      user: config.user,
      password: config.password,
    }
  } catch (error) {
    console.error('Error parsing SMTP config:', error)
    return null
  }
}

/**
 * Tests an email configuration by sending a test email
 */
export async function testEmailConfig(config: EmailConfig, testEmail: string): Promise<boolean> {
  try {
    const transporter = createTransporter(config)
    
    const mailOptions = {
      from: `"YogaBot Live" <${config.user}>`,
      to: testEmail,
      subject: 'Email Configuration Test - YogaBot Live',
      html: `
        <html>
          <body style="font-family: Arial, sans-serif;">
            <h2>Email Configuration Test</h2>
            <p>This is a test email to verify your SMTP configuration is working correctly.</p>
            <p>If you received this email, your configuration is set up properly! 🎉</p>
            <hr>
            <p style="color: #666; font-size: 12px;">© 2025 YogaBot Live</p>
          </body>
        </html>
      `,
    }

    await transporter.sendMail(mailOptions)
    return true
  } catch (error) {
    console.error('Email configuration test failed:', error)
    return false
  }
}
