import { prisma } from './prisma'
import { sendSubscriptionWarningEmail } from './email-service'

/**
 * Checks all active subscriptions for high token usage and sends warning emails
 */
export async function checkTokenUsageAndSendWarnings(): Promise<void> {
  try {
    console.log('Starting token usage check...')

    // Get all active subscriptions with high token usage (90% or more)
    const subscriptions = await prisma.subscription.findMany({
      where: {
        status: 'active'
      },
      include: {
        user: true,
        plan: true
      }
    })

    const warningsToSend = []

    for (const subscription of subscriptions) {
      const tokenLimit = subscription.plan.features?.tokenLimit || 0
      const tokensUsed = subscription.tokensUsedThisPeriod
      const percentageUsed = tokenLimit > 0 ? Math.round((tokensUsed / tokenLimit) * 100) : 0

      // Send warning if usage is 90% or higher
      if (percentageUsed >= 90) {
        // Check if we've already sent a warning for this period
        const warningKey = `token_warning_${subscription.id}_${subscription.currentPeriodEnd.getTime()}`
        
        // In a production environment, you'd want to store this in Redis or a database table
        // For now, we'll send the warning (could be optimized to prevent duplicate emails)
        
        warningsToSend.push({
          subscription,
          percentageUsed,
          tokensUsed,
          tokenLimit
        })
      }
    }

    console.log(`Found ${warningsToSend.length} subscriptions requiring warnings`)

    // Send warning emails
    for (const warning of warningsToSend) {
      try {
        await sendSubscriptionWarningEmail(warning.subscription.user.email, {
          userName: warning.subscription.user.name || 'User',
          tokensUsed: warning.tokensUsed,
          tokenLimit: warning.tokenLimit,
          percentageUsed: warning.percentageUsed,
          planName: warning.subscription.plan.name,
          dashboardUrl: `${process.env.NEXTAUTH_URL}/dashboard`
        })

        console.log(`Warning email sent to ${warning.subscription.user.email} (${warning.percentageUsed}% usage)`)
      } catch (emailError) {
        console.error(`Failed to send warning email to ${warning.subscription.user.email}:`, emailError)
      }
    }

    console.log('Token usage check completed')

  } catch (error) {
    console.error('Error in token usage check:', error)
  }
}

/**
 * Resets token usage for subscriptions that have entered a new billing period
 */
export async function resetTokenUsageForNewPeriods(): Promise<void> {
  try {
    console.log('Checking for subscriptions to reset...')

    const now = new Date()

    // Find subscriptions where the current period has ended
    const subscriptionsToReset = await prisma.subscription.findMany({
      where: {
        currentPeriodEnd: {
          lt: now
        },
        status: 'active'
      }
    })

    if (subscriptionsToReset.length === 0) {
      console.log('No subscriptions need token usage reset')
      return
    }

    // Reset token usage for these subscriptions
    const resetPromises = subscriptionsToReset.map(subscription => {
      // Calculate next period end (30 days from current period end)
      const nextPeriodEnd = new Date(subscription.currentPeriodEnd)
      nextPeriodEnd.setDate(nextPeriodEnd.getDate() + 30)

      return prisma.subscription.update({
        where: { id: subscription.id },
        data: {
          tokensUsedThisPeriod: 0,
          sessionsThisPeriod: 0,
          currentPeriodEnd: nextPeriodEnd
        }
      })
    })

    await Promise.all(resetPromises)

    console.log(`Reset token usage for ${subscriptionsToReset.length} subscriptions`)

  } catch (error) {
    console.error('Error resetting token usage:', error)
  }
}

/**
 * Increments token usage for a subscription
 */
export async function incrementTokenUsage(subscriptionId: string, tokenCount: number): Promise<void> {
  try {
    await prisma.subscription.update({
      where: { id: subscriptionId },
      data: {
        tokensUsedThisPeriod: {
          increment: tokenCount
        }
      }
    })
  } catch (error) {
    console.error('Error incrementing token usage:', error)
  }
}

/**
 * Increments session count for a subscription
 */
export async function incrementSessionCount(subscriptionId: string): Promise<void> {
  try {
    await prisma.subscription.update({
      where: { id: subscriptionId },
      data: {
        sessionsThisPeriod: {
          increment: 1
        }
      }
    })
  } catch (error) {
    console.error('Error incrementing session count:', error)
  }
}

/**
 * Gets current usage statistics for a subscription
 */
export async function getUsageStats(subscriptionId: string): Promise<{
  tokensUsed: number
  tokenLimit: number
  percentageUsed: number
  sessionsUsed: number
  daysUntilReset: number
} | null> {
  try {
    const subscription = await prisma.subscription.findUnique({
      where: { id: subscriptionId },
      include: {
        plan: true
      }
    })

    if (!subscription) {
      return null
    }

    const tokenLimit = subscription.plan.features?.tokenLimit || 0
    const tokensUsed = subscription.tokensUsedThisPeriod
    const percentageUsed = tokenLimit > 0 ? Math.round((tokensUsed / tokenLimit) * 100) : 0
    
    const now = new Date()
    const daysUntilReset = Math.ceil(
      (subscription.currentPeriodEnd.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
    )

    return {
      tokensUsed,
      tokenLimit,
      percentageUsed,
      sessionsUsed: subscription.sessionsThisPeriod,
      daysUntilReset: Math.max(0, daysUntilReset)
    }

  } catch (error) {
    console.error('Error getting usage stats:', error)
    return null
  }
}
