import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// Rate limiting store (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

export interface RateLimitOptions {
  requests: number
  window: string // e.g., '1h', '1m', '1d'
}

// Parse time window to milliseconds
function parseTimeWindow(window: string): number {
  const match = window.match(/^(\d+)([hmsd])$/)
  if (!match) throw new Error(`Invalid time window format: ${window}`)
  
  const [, amount, unit] = match
  const multipliers = {
    s: 1000,
    m: 60 * 1000,
    h: 60 * 60 * 1000,
    d: 24 * 60 * 60 * 1000
  }
  
  return parseInt(amount) * multipliers[unit as keyof typeof multipliers]
}

// Apply rate limiting
export async function applyRateLimit(
  request: NextRequest, 
  options: RateLimitOptions
): Promise<void> {
  const clientIP = request.headers.get('x-forwarded-for')?.split(',')[0] || 
    request.headers.get('x-real-ip') || 
    'unknown'
  
  const key = `rate_limit:${clientIP}`
  const now = Date.now()
  const windowMs = parseTimeWindow(options.window)
  
  const existing = rateLimitStore.get(key)
  
  if (!existing || now > existing.resetTime) {
    // First request or window expired
    rateLimitStore.set(key, {
      count: 1,
      resetTime: now + windowMs
    })
    return
  }
  
  if (existing.count >= options.requests) {
    throw new Error(`Rate limit exceeded. Max ${options.requests} requests per ${options.window}`)
  }
  
  existing.count++
  rateLimitStore.set(key, existing)
}

// Enhanced user verification with rate limiting
export async function verifyUserWithRateLimit(
  request: NextRequest,
  rateLimit: RateLimitOptions = { requests: 100, window: '1h' }
) {
  await applyRateLimit(request, rateLimit)
  
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) {
    throw new Error("Not authenticated")
  }
  return { user: session.user }
}

// Enhanced admin verification with rate limiting
export async function verifyAdminWithRateLimit(
  request: NextRequest,
  rateLimit: RateLimitOptions = { requests: 100, window: '1h' }
) {
  await applyRateLimit(request, rateLimit)
  
  const session = await getServerSession(authOptions)
  
  if (!session?.user) {
    throw new Error("Not authenticated")
  }
  
  if (session.user.role !== 'ADMIN') {
    throw new Error("Not authorized")
  }
  
  return { user: session.user }
}

// Validate resource ownership
export async function validateResourceOwnership(
  userId: string,
  resourceId: string,
  resourceType: 'chatbot' | 'knowledgeBase' | 'user'
): Promise<void> {
  let resource: { id: string } | null = null
  
  switch (resourceType) {
    case 'chatbot':
      resource = await prisma.chatbot.findFirst({
        where: { id: resourceId, userId },
        select: { id: true }
      })
      break
      
    case 'knowledgeBase':
      resource = await prisma.knowledgeBase.findFirst({
        where: { id: resourceId, userId },
        select: { id: true }
      })
      break
      
    case 'user':
      if (resourceId !== userId) {
        throw new Error('Access denied: Resource ownership validation failed')
      }
      return
      
    default:
      throw new Error(`Unknown resource type: ${resourceType}`)
  }
  
  if (!resource) {
    throw new Error('Access denied: Resource not found or access denied')
  }
}

// Standardized error response creator
export function createErrorResponse(message: string, status: number): NextResponse {
  return NextResponse.json(
    { 
      error: message,
      timestamp: new Date().toISOString()
    }, 
    { status }
  )
}

// Enhanced route error handler
export function handleRouteError(error: unknown): NextResponse {
  console.error('Route error:', error)
  
  if (error instanceof Error) {
    if (error.message.includes('Rate limit exceeded')) {
      return createErrorResponse(error.message, 429)
    }
    if (error.message.includes('Not authenticated')) {
      return createErrorResponse(error.message, 401)
    }
    if (error.message.includes('Not authorized') || error.message.includes('Access denied')) {
      return createErrorResponse(error.message, 403)
    }
  }
  
  return createErrorResponse('Internal server error', 500)
}

// Pre-configured rate limits
export const RateLimits = {
  STRICT: { requests: 10, window: '1m' },
  NORMAL: { requests: 100, window: '1h' },
  RELAXED: { requests: 1000, window: '1h' },
  PUBLIC: { requests: 50, window: '1m' }
} as const
