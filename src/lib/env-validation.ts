import { z } from 'zod'

// Define the schema for required environment variables
const envSchema = z.object({
  // Database
  POSTGRES_PRISMA_URL: z.string().min(1, "POSTGRES_PRISMA_URL is required"),
  
  // Authentication
  NEXTAUTH_SECRET: z.string().min(1, "NEXTAUTH_SECRET is required"),
  NEXTAUTH_URL: z.string().url("NEXTAUTH_URL must be a valid URL"),
  
  // LLM Services
  GEMINI_API_KEY: z.string().min(1, "GEMINI_API_KEY is required"),
  
  // Optional environment variables with defaults
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  
  // Optional services (not required for basic functionality)
  OPENROUTER_API_KEY: z.string().optional(),
  ENCRYPTION_KEY: z.string().optional(),
  ABLY_API_KEY: z.string().optional(),
  QSTASH_URL: z.string().optional(),
  QSTASH_TOKEN: z.string().optional(),
  QSTASH_CURRENT_SIGNING_KEY: z.string().optional(),
  QSTASH_NEXT_SIGNING_KEY: z.string().optional(),

  // Payment processing (Razorpay)
  RAZORPAY_KEY_ID: z.string().optional(),
  RAZORPAY_KEY_SECRET: z.string().optional(),
  RAZORPAY_WEBHOOK_SECRET: z.string().optional(),

  // Email system (SMTP)
  SMTP_HOST: z.string().optional(),
  SMTP_PORT: z.string().optional(),
  SMTP_SECURE: z.string().optional(),
  SMTP_USER: z.string().optional(),
  SMTP_PASSWORD: z.string().optional(),
})

export type Env = z.infer<typeof envSchema>

/**
 * Validates environment variables and throws an error if any required variables are missing
 * @returns Validated environment variables
 */
export function validateEnv(): Env {
  try {
    const env = envSchema.parse(process.env)
    
    // Additional validation warnings for optional but recommended variables
    const warnings: string[] = []
    
    if (!env.ENCRYPTION_KEY) {
      warnings.push("ENCRYPTION_KEY is not set - BYOK and SMTP encryption will not work")
    }
    
    if (!env.ABLY_API_KEY) {
      warnings.push("ABLY_API_KEY is not set - real-time chat features will not work")
    }
    
    if (!env.QSTASH_TOKEN) {
      warnings.push("QSTASH_TOKEN is not set - background job processing will use direct HTTP calls")
    }

    if (!env.RAZORPAY_KEY_ID || !env.RAZORPAY_KEY_SECRET) {
      warnings.push("RAZORPAY credentials are not set - billing and subscription features will not work")
    }

    if (!env.SMTP_HOST || !env.SMTP_USER || !env.SMTP_PASSWORD) {
      warnings.push("SMTP credentials are not set - automated email features will not work")
    }
    
    // Log warnings in development
    if (warnings.length > 0 && env.NODE_ENV === 'development') {
      console.warn('⚠️  Environment Variable Warnings:')
      warnings.forEach(warning => console.warn(`   - ${warning}`))
      console.warn('')
    }
    
    return env
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join('\n')
      
      console.error('❌ Environment validation failed!')
      console.error('Missing or invalid environment variables:')
      console.error(missingVars)
      console.error('\nPlease check your .env file and ensure all required variables are set.')
      console.error('See .env.example for reference.')
      
      throw new Error(`Environment validation failed: Missing required environment variables`)
    }
    throw error
  }
}

/**
 * Get validated environment variables (cached after first call)
 */
let cachedEnv: Env | null = null

export function getEnv(): Env {
  if (!cachedEnv) {
    cachedEnv = validateEnv()
  }
  return cachedEnv
}

/**
 * Check if all required environment variables are available
 * @returns boolean indicating if environment is properly configured
 */
export function isEnvValid(): boolean {
  try {
    validateEnv()
    return true
  } catch {
    return false
  }
}
