import crypto from 'crypto'

const ALGORITHM = 'aes-256-gcm'
const IV_LENGTH = 16 // For GCM, this is always 16
const SALT_LENGTH = 64
const TAG_LENGTH = 16

/**
 * Encrypts a string using AES-256-GCM
 * @param text - The text to encrypt
 * @param encryptionKey - The encryption key (optional, uses env var if not provided)
 * @returns Encrypted string in format: salt:iv:tag:encrypted
 */
export function encrypt(text: string, encryptionKey?: string): string {
  if (!text) {
    throw new Error('Text to encrypt cannot be empty')
  }

  const key = encryptionKey || process.env.ENCRYPTION_KEY
  if (!key) {
    throw new Error('ENCRYPTION_KEY environment variable is not set')
  }

  // Generate random salt and IV
  const salt = crypto.randomBytes(SALT_LENGTH)
  const iv = crypto.randomBytes(IV_LENGTH)

  // Derive key from password and salt
  const derivedKey = crypto.pbkdf2Sync(key, salt, 100000, 32, 'sha256')

  // Create cipher
  const cipher = crypto.createCipher(ALGORITHM, derivedKey)
  cipher.setAAD(Buffer.from('YogaBot-Live', 'utf8')) // Additional authenticated data

  // Encrypt the text
  let encrypted = cipher.update(text, 'utf8', 'hex')
  encrypted += cipher.final('hex')

  // Get the authentication tag
  const tag = cipher.getAuthTag()

  // Combine salt, iv, tag, and encrypted data
  const result = [
    salt.toString('hex'),
    iv.toString('hex'),
    tag.toString('hex'),
    encrypted
  ].join(':')

  return result
}

/**
 * Decrypts a string encrypted with the encrypt function
 * @param encryptedData - The encrypted string in format: salt:iv:tag:encrypted
 * @param encryptionKey - The encryption key (optional, uses env var if not provided)
 * @returns Decrypted string
 */
export function decrypt(encryptedData: string, encryptionKey?: string): string {
  if (!encryptedData) {
    throw new Error('Encrypted data cannot be empty')
  }

  const key = encryptionKey || process.env.ENCRYPTION_KEY
  if (!key) {
    throw new Error('ENCRYPTION_KEY environment variable is not set')
  }

  try {
    // Split the encrypted data
    const parts = encryptedData.split(':')
    if (parts.length !== 4) {
      throw new Error('Invalid encrypted data format')
    }

    const [saltHex, ivHex, tagHex, encrypted] = parts

    // Convert hex strings back to buffers
    const salt = Buffer.from(saltHex, 'hex')
    const iv = Buffer.from(ivHex, 'hex')
    const tag = Buffer.from(tagHex, 'hex')

    // Derive the same key
    const derivedKey = crypto.pbkdf2Sync(key, salt, 100000, 32, 'sha256')

    // Create decipher
    const decipher = crypto.createDecipher(ALGORITHM, derivedKey)
    decipher.setAAD(Buffer.from('YogaBot-Live', 'utf8'))
    decipher.setAuthTag(tag)

    // Decrypt the data
    let decrypted = decipher.update(encrypted, 'hex', 'utf8')
    decrypted += decipher.final('utf8')

    return decrypted

  } catch (error) {
    throw new Error(`Decryption failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Safely encrypts data, returning null if encryption fails
 * @param text - The text to encrypt
 * @returns Encrypted string or null if encryption fails
 */
export function safeEncrypt(text: string): string | null {
  try {
    return encrypt(text)
  } catch (error) {
    console.error('Encryption failed:', error)
    return null
  }
}

/**
 * Safely decrypts data, returning null if decryption fails
 * @param encryptedData - The encrypted data
 * @returns Decrypted string or null if decryption fails
 */
export function safeDecrypt(encryptedData: string): string | null {
  try {
    return decrypt(encryptedData)
  } catch (error) {
    console.error('Decryption failed:', error)
    return null
  }
}

/**
 * Checks if encryption is available (ENCRYPTION_KEY is set)
 * @returns boolean indicating if encryption is available
 */
export function isEncryptionAvailable(): boolean {
  return !!process.env.ENCRYPTION_KEY
}
