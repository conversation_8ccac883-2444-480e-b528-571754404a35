"use client"

import { usePathname } from "next/navigation"
import { Head<PERSON> } from "./header"

interface DynamicHeaderProps {
  user: {
    id: string
    email: string
    name?: string | null
    role: string
  }
}

// Define page-specific header content
const pageHeaders: Record<string, { title: string; subtitle: string }> = {
  "/dashboard": {
    title: "Dashboard",
    subtitle: `Welcome back to your workspace`
  },
  "/dashboard/chatbots": {
    title: "Chatbots",
    subtitle: "Manage your AI-powered chatbots"
  },
  "/dashboard/kb": {
    title: "Knowledge Base",
    subtitle: "Configure your chatbot's knowledge about your yoga school"
  },
  "/dashboard/live": {
    title: "Live Chat",
    subtitle: "Monitor active conversations and take over when needed"
  },
  "/dashboard/leads": {
    title: "Leads",
    subtitle: "View leads and conversation analytics"
  },
  "/dashboard/settings": {
    title: "Settings",
    subtitle: "Configure your chatbot appearance and behavior"
  },
  // Admin pages
  "/dashboard/admin": {
    title: "Admin Dashboard",
    subtitle: "Complete control over the YogaBot Live platform"
  },
  "/dashboard/admin/revenue": {
    title: "Revenue & Billing",
    subtitle: "Monitor subscription revenue, billing issues, and financial metrics"
  },
  "/dashboard/admin/users": {
    title: "User Management",
    subtitle: "Manage user accounts, subscriptions, and billing issues"
  },
  "/dashboard/admin/plans": {
    title: "Subscription Plans",
    subtitle: "Create and manage subscription plans and pricing"
  },
  "/dashboard/admin/analytics": {
    title: "Platform Analytics",
    subtitle: "Comprehensive insights into platform usage, growth, and performance"
  },
  "/dashboard/admin/system": {
    title: "System Health",
    subtitle: "Monitor system status and performance"
  },
  "/dashboard/admin/settings": {
    title: "Platform Settings",
    subtitle: "Configure system-wide settings and features"
  }
}

export function DynamicHeader({ user }: DynamicHeaderProps) {
  const pathname = usePathname()
  
  // Get header content for current page, with fallback
  const headerContent = pageHeaders[pathname] || {
    title: "Dashboard",
    subtitle: "Welcome back to your workspace"
  }

  // Handle dynamic user pages (like /dashboard/admin/users/[id])
  if (pathname.startsWith("/dashboard/admin/users/") && pathname !== "/dashboard/admin/users") {
    headerContent.title = "User Details"
    headerContent.subtitle = "View and manage individual user account"
  }

  return (
    <Header 
      user={user} 
      title={headerContent.title}
      subtitle={headerContent.subtitle}
    />
  )
}
