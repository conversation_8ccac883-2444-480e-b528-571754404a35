"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import {
  Home,
  Settings,
  Users,
  Bot,
  Database,
  MessageSquare,
  BarChart3,
  Crown,
  Activity,
  DollarSign,
  TrendingUp,
  Cog
} from "lucide-react"

interface SidebarProps {
  user: {
    id: string
    email: string
    name?: string | null
    role: string
  }
}

export function Sidebar({ user }: SidebarProps) {
  const pathname = usePathname()

  const navigation = [
    { name: "Dashboard", href: "/dashboard", icon: Home },
    { name: "Chatbots", href: "/dashboard/chatbots", icon: Bo<PERSON> },
    { name: "Knowledge Base", href: "/dashboard/kb", icon: Database },
    { name: "Live Chat", href: "/dashboard/live", icon: MessageSquare },
    { name: "Leads", href: "/dashboard/leads", icon: BarChart3 },
    { name: "Settings", href: "/dashboard/settings", icon: Settings },
  ]

  const adminNavigation = [
    { name: "Admin Dashboard", href: "/dashboard/admin", icon: Crown },
    { name: "Revenue & Billing", href: "/dashboard/admin/revenue", icon: DollarSign },
    { name: "User Management", href: "/dashboard/admin/users", icon: Users },
    { name: "Subscription Plans", href: "/dashboard/admin/plans", icon: Settings },
    { name: "Analytics", href: "/dashboard/admin/analytics", icon: TrendingUp },
    { name: "System Health", href: "/dashboard/admin/system", icon: Activity },
    { name: "Platform Settings", href: "/dashboard/admin/settings", icon: Cog },
  ]

  return (
    <div className="fixed left-0 top-0 h-full w-72 bg-gradient-to-b from-slate-900 via-slate-800 to-slate-900 shadow-2xl border-r border-slate-700">
      <div className="p-3 border-b border-slate-700">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
            <span className="text-white font-bold text-lg">Y</span>
          </div>
          <div>
            <h1 className="text-xl font-bold text-white">YogaBot Live</h1>
            <p className="text-slate-300 text-sm">AI-Powered Platform</p>
          </div>
        </div>
      </div>

      <nav className="mt-6">
        <div className="px-4">
          <ul className="space-y-2">
            {navigation.map((item) => {
              const isActive = pathname === item.href
              return (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className={`
                      group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200
                      ${isActive
                        ? "bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg"
                        : "text-slate-300 hover:bg-slate-800/50 hover:text-white"
                      }
                    `}
                  >
                    <item.icon
                      className={`
                        mr-3 h-5 w-5 flex-shrink-0
                        ${isActive ? "text-white" : "text-slate-400 group-hover:text-slate-300"}
                      `}
                    />
                    {item.name}
                  </Link>
                </li>
              )
            })}
          </ul>
        </div>

        {user.role === "ADMIN" && (
          <div className="mt-8 px-4">
            <div className="mb-4">
              <h3 className="px-3 text-xs font-semibold text-slate-400 uppercase tracking-wider">
                Admin Panel
              </h3>
            </div>
            <ul className="space-y-2">
              {adminNavigation.map((item) => {
                const isActive = pathname === item.href
                return (
                  <li key={item.name}>
                    <Link
                      href={item.href}
                      className={`
                        group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200
                        ${isActive
                          ? "bg-gradient-to-r from-red-500 to-pink-600 text-white shadow-lg"
                          : "text-slate-300 hover:bg-slate-800/50 hover:text-white"
                        }
                      `}
                    >
                      <item.icon
                        className={`
                          mr-3 h-5 w-5 flex-shrink-0
                          ${isActive ? "text-white" : "text-slate-400 group-hover:text-slate-300"}
                        `}
                      />
                      {item.name}
                    </Link>
                  </li>
                )
              })}
            </ul>
          </div>
        )}
      </nav>
    </div>
  )
}
