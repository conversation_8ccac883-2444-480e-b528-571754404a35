"use client"

import { useState, useEffect } from 'react'
import { Save, FileText, CheckCircle, AlertCircle } from 'lucide-react'
import { useRouter } from 'next/navigation'

interface KnowledgeBase {
  id: string
  name: string
  kbType: string
  simpleKbText: string | null
}

interface SimpleKBTextEditorProps {
  knowledgeBase: KnowledgeBase
  characterLimit: number
}

export default function SimpleKBTextEditor({ knowledgeBase, characterLimit }: SimpleKBTextEditorProps) {
  const router = useRouter()
  const [content, setContent] = useState(knowledgeBase.simpleKbText || '')
  const [isLoading, setIsLoading] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle')

  const characterCount = content.length
  const isOverLimit = characterCount > characterLimit
  const percentUsed = (characterCount / characterLimit) * 100

  useEffect(() => {
    setHasChanges(content !== (knowledgeBase.simpleKbText || ''))
  }, [content, knowledgeBase.simpleKbText])

  const handleSave = async () => {
    if (isOverLimit) {
      alert(`Content exceeds the character limit of ${characterLimit.toLocaleString()} characters.`)
      return
    }

    setIsLoading(true)
    setSaveStatus('saving')

    try {
      const response = await fetch(`/api/user/knowledge-bases/${knowledgeBase.id}/content`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          simpleKbText: content.trim()
        })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to save content')
      }

      setSaveStatus('saved')
      setHasChanges(false)
      
      // Auto-hide success message after 3 seconds
      setTimeout(() => {
        if (saveStatus === 'saved') {
          setSaveStatus('idle')
        }
      }, 3000)

      // Refresh the page to show updated data
      router.refresh()
    } catch (error) {
      console.error('Error saving content:', error)
      setSaveStatus('error')
      alert(error instanceof Error ? error.message : 'Failed to save content. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const getCharacterCountColor = () => {
    if (isOverLimit) return 'text-red-600'
    if (percentUsed > 90) return 'text-amber-600'
    if (percentUsed > 75) return 'text-yellow-600'
    return 'text-gray-600'
  }

  const getProgressBarColor = () => {
    if (isOverLimit) return 'bg-red-500'
    if (percentUsed > 90) return 'bg-amber-500'
    if (percentUsed > 75) return 'bg-yellow-500'
    return 'bg-green-500'
  }

  return (
    <div className="space-y-6">
      {/* Content Editor */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <FileText className="w-6 h-6 text-blue-600" />
            <h3 className="text-xl font-semibold text-gray-900">Text Content</h3>
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Knowledge Base Content
            </label>
            <textarea
              value={content}
              onChange={(e) => setContent(e.target.value)}
              rows={20}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none ${
                isOverLimit ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Enter your knowledge base content here. This text will be used to answer questions from your chatbot visitors..."
            />
          </div>

          {/* Character Count and Progress */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className={`text-sm font-medium ${getCharacterCountColor()}`}>
                {characterCount.toLocaleString()} / {characterLimit.toLocaleString()} characters
              </span>
              <span className={`text-sm ${getCharacterCountColor()}`}>
                {percentUsed.toFixed(1)}% used
              </span>
            </div>
            
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${getProgressBarColor()}`}
                style={{ width: `${Math.min(percentUsed, 100)}%` }}
              ></div>
            </div>
            
            {isOverLimit && (
              <p className="text-sm text-red-600">
                Content exceeds the character limit. Please reduce the text by {(characterCount - characterLimit).toLocaleString()} characters.
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          {saveStatus === 'saved' && (
            <div className="flex items-center space-x-2 text-green-600">
              <CheckCircle className="w-4 h-4" />
              <span className="text-sm">Content saved successfully!</span>
            </div>
          )}
          
          {saveStatus === 'error' && (
            <div className="flex items-center space-x-2 text-red-600">
              <AlertCircle className="w-4 h-4" />
              <span className="text-sm">Failed to save content</span>
            </div>
          )}
        </div>

        <div className="flex space-x-3">
          <button
            onClick={() => router.back()}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
          >
            Cancel
          </button>
          
          <button
            onClick={handleSave}
            disabled={isLoading || !hasChanges || isOverLimit}
            className="inline-flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Save className="w-4 h-4 mr-2" />
            {isLoading ? 'Saving...' : 'Save Content'}
          </button>
        </div>
      </div>

      {/* Help Text */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h4 className="text-sm font-medium text-gray-900 mb-2">Tips for Better Content</h4>
        <ul className="text-sm text-gray-600 space-y-1">
          <li>• Write clear, comprehensive information about your business</li>
          <li>• Include details about services, policies, and frequently asked questions</li>
          <li>• Use natural language that your visitors would understand</li>
          <li>• Simple knowledge bases store text as-is - no processing required</li>
        </ul>
      </div>
    </div>
  )
}
