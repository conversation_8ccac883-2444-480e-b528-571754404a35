"use client"

import { useState } from 'react'
import { Database, Bot, Save, Trash2, <PERSON><PERSON><PERSON><PERSON>gle, FileText, Layers } from 'lucide-react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface Chatbot {
  id: string
  approvedDomain: string
  assignedKbId: string | null
}

interface KnowledgeBase {
  id: string
  name: string
  description: string | null
  kbType: string
  needsReprocessing: boolean
  lastProcessedAt: Date | string | null
  assignedChatbot: {
    id: string
    approvedDomain: string
  } | null
  _count: {
    knowledgeChunks: number
  }
}

interface KnowledgeBaseSettingsClientProps {
  knowledgeBase: KnowledgeBase
  chatbots: Chatbot[]
}

export default function KnowledgeBaseSettingsClient({ knowledgeBase, chatbots }: KnowledgeBaseSettingsClientProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [formData, setFormData] = useState({
    name: knowledgeBase.name,
    description: knowledgeBase.description || '',
    assignedChatbotId: knowledgeBase.assignedChatbot?.id || ''
  })

  const handleSave = async () => {
    setIsLoading(true)
    try {
      // Update KB basic settings
      const response = await fetch(`/api/user/knowledge-bases/${knowledgeBase.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          description: formData.description,
          assignedChatbotId: formData.assignedChatbotId || null
        })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update knowledge base')
      }

      // Refresh the page to show updated data
      router.refresh()
    } catch (error) {
      console.error('Error updating knowledge base:', error)
      alert(error instanceof Error ? error.message : 'Failed to update knowledge base. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleDelete = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/user/knowledge-bases/${knowledgeBase.id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to delete knowledge base')
      }

      router.push('/dashboard/kb')
    } catch (error) {
      console.error('Error deleting knowledge base:', error)
      alert('Failed to delete knowledge base. Please try again.')
      setIsLoading(false)
    }
  }

  const handleEmbed = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/user/knowledge-bases/${knowledgeBase.id}/embed`, {
        method: 'POST'
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to process knowledge base')
      }

      // Refresh the page to show updated data
      router.refresh()
    } catch (error) {
      console.error('Error processing knowledge base:', error)
      alert(error instanceof Error ? error.message : 'Failed to process knowledge base. Please try again.')
      setIsLoading(false)
    }
  }

  // Filter available chatbots (exclude ones with other KBs assigned)
  const availableChatbots = chatbots.filter(chatbot => 
    !chatbot.assignedKbId || chatbot.assignedKbId === knowledgeBase.id
  )

  return (
    <div className="space-y-6">
      {/* Basic Settings */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-6">
          <Database className="w-6 h-6 text-green-600" />
          <h3 className="text-xl font-semibold text-gray-900">Basic Settings</h3>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Knowledge Base Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
              placeholder="My Knowledge Base"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
              placeholder="Describe what this knowledge base contains..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Knowledge Base Type
            </label>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
              <div className="flex items-center space-x-3">
                {knowledgeBase.kbType === 'simple' ? (
                  <FileText className="w-5 h-5 text-blue-600" />
                ) : (
                  <Layers className="w-5 h-5 text-green-600" />
                )}
                <div>
                  <span className="font-medium text-gray-900 capitalize">
                    {knowledgeBase.kbType} Knowledge Base
                  </span>
                  {knowledgeBase.kbType === 'structured' && (
                    <span className="text-sm text-gray-500 block">
                      ({knowledgeBase._count.knowledgeChunks} chunks processed)
                    </span>
                  )}
                </div>
              </div>

              {knowledgeBase.needsReprocessing && knowledgeBase._count.knowledgeChunks > 0 && (
                <button
                  onClick={handleEmbed}
                  disabled={isLoading}
                  className="inline-flex items-center px-3 py-1 bg-amber-600 text-white rounded-md hover:bg-amber-700 transition-colors disabled:opacity-50 text-sm"
                >
                  Process Changes
                </button>
              )}
            </div>

            {knowledgeBase.needsReprocessing && (
              <p className="text-sm text-amber-600 mt-1">
                Content has been modified and needs reprocessing to update your chatbot's knowledge.
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Chatbot Assignment */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-6">
          <Bot className="w-6 h-6 text-blue-600" />
          <h3 className="text-xl font-semibold text-gray-900">Chatbot Assignment</h3>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Assign to Chatbot
            </label>
            <select
              value={formData.assignedChatbotId}
              onChange={(e) => setFormData(prev => ({ ...prev, assignedChatbotId: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              <option value="">Not assigned to any chatbot</option>
              {availableChatbots.map((chatbot) => (
                <option key={chatbot.id} value={chatbot.id}>
                  {chatbot.approvedDomain}
                </option>
              ))}
            </select>
            <p className="text-sm text-gray-500 mt-1">
              Each chatbot can only have one knowledge base assigned at a time
            </p>
          </div>

          {formData.assignedChatbotId && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <p className="text-sm text-blue-800">
                <strong>Selected:</strong> {availableChatbots.find(c => c.id === formData.assignedChatbotId)?.approvedDomain}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Content Management */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-4">
          {knowledgeBase.kbType === 'simple' ? (
            <FileText className="w-6 h-6 text-blue-600" />
          ) : (
            <Layers className="w-6 h-6 text-green-600" />
          )}
          <h3 className="text-xl font-semibold text-gray-900">Content Management</h3>
        </div>

        <div className="flex items-center space-x-4">
          <Link
            href={`/dashboard/kb/${knowledgeBase.id}/edit`}
            className={`inline-flex items-center px-4 py-2 rounded-lg text-white transition-colors ${
              knowledgeBase.kbType === 'simple' 
                ? 'bg-blue-600 hover:bg-blue-700' 
                : 'bg-green-600 hover:bg-green-700'
            }`}
          >
            {knowledgeBase.kbType === 'simple' ? (
              <FileText className="w-4 h-4 mr-2" />
            ) : (
              <Layers className="w-4 h-4 mr-2" />
            )}
            Edit {knowledgeBase.kbType === 'simple' ? 'Text Content' : 'Structured Data'}
          </Link>
          <p className="text-sm text-gray-600">
            {knowledgeBase.kbType === 'simple' 
              ? 'Edit the text content of your knowledge base'
              : 'Manage structured data including school info, teachers, FAQs, and more'
            }
          </p>
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-between items-center">
        <button
          onClick={() => setShowDeleteConfirm(true)}
          disabled={isLoading}
          className="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
        >
          <Trash2 className="w-4 h-4 mr-2" />
          Delete Knowledge Base
        </button>

        <button
          onClick={handleSave}
          disabled={isLoading}
          className="inline-flex items-center px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
        >
          <Save className="w-4 h-4 mr-2" />
          {isLoading ? 'Saving...' : 'Save Changes'}
        </button>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center space-x-3 mb-4">
              <AlertTriangle className="w-6 h-6 text-red-600" />
              <h3 className="text-lg font-semibold text-gray-900">Delete Knowledge Base</h3>
            </div>
            
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete "{knowledgeBase.name}"? This action cannot be undone. 
              All content and {knowledgeBase._count.knowledgeChunks} processed chunks will be permanently deleted.
              {knowledgeBase.assignedChatbot && (
                <span className="block mt-2 font-medium text-amber-600">
                  This will also unassign it from the chatbot: {knowledgeBase.assignedChatbot.approvedDomain}
                </span>
              )}
            </p>
            
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                disabled={isLoading}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                onClick={handleDelete}
                disabled={isLoading}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
              >
                {isLoading ? 'Deleting...' : 'Delete Permanently'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
