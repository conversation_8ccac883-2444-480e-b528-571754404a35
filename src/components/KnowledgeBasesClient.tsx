"use client"

import { useState } from 'react'
import { Database, Plus, FileText, <PERSON>tings, Trash2, Bot, ExternalLink } from 'lucide-react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface KnowledgeBase {
  id: string
  name: string
  description: string | null
  kbType: string
  assignedChatbot: {
    id: string
    approvedDomain: string
  } | null
  _count: {
    knowledgeChunks: number
  }
  createdAt: Date | string
}

interface User {
  id: string
  role: string
  subscription: {
    plan: {
      name: string
      features: any
    }
  } | null
  knowledgeBases: KnowledgeBase[]
}

interface KnowledgeBasesClientProps {
  user: User
}

export default function KnowledgeBasesClient({ user }: KnowledgeBasesClientProps) {
  const router = useRouter()
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const knowledgeBases = user.knowledgeBases
  const subscription = user.subscription
  const plan = subscription?.plan

  // Check limits (admins have no limits)
  const planFeatures = plan?.features as any
  const kbLimit = planFeatures?.kbLimit || 1
  const planKbType = planFeatures?.kbType || 'simple'
  const canCreateMore = user.role === 'ADMIN' || knowledgeBases.length < kbLimit

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <Database className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Knowledge Bases</p>
              <p className="text-2xl font-bold text-gray-900">{knowledgeBases.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <FileText className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Plan Limit</p>
              <p className="text-2xl font-bold text-gray-900">{knowledgeBases.length} / {kbLimit}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <Bot className="h-8 w-8 text-purple-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Plan Type</p>
              <p className="text-2xl font-bold text-gray-900 capitalize">{planKbType}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Create KB Button */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Your Knowledge Bases</h2>
        <button
          onClick={() => setShowCreateModal(true)}
          disabled={!canCreateMore}
          className={`inline-flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${
            canCreateMore
              ? 'bg-green-600 text-white hover:bg-green-700'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          <Plus className="w-4 h-4 mr-2" />
          Create Knowledge Base
        </button>
      </div>

      {!canCreateMore && user.role !== 'ADMIN' && (
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
          <p className="text-sm text-amber-800">
            <strong>Limit Reached:</strong> You've reached your plan's knowledge base limit ({kbLimit}).
            Upgrade your plan to create more knowledge bases.
          </p>
        </div>
      )}

      {/* Knowledge Bases List */}
      {knowledgeBases.length === 0 ? (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
          <Database className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">No Knowledge Bases</h3>
          <p className="text-gray-600 mb-6">
            Create your first knowledge base to get started. You can assign it to chatbots later.
          </p>
          <button
            onClick={() => setShowCreateModal(true)}
            className="inline-flex items-center px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
          >
            <Plus className="w-5 h-5 mr-2" />
            Create Your First Knowledge Base
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {knowledgeBases.map((kb) => (
            <div key={kb.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  {kb.kbType === 'simple' ? (
                    <FileText className="w-6 h-6 text-blue-600" />
                  ) : (
                    <Database className="w-6 h-6 text-green-600" />
                  )}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{kb.name}</h3>
                    <p className="text-sm text-gray-600 capitalize">{kb.kbType} Knowledge Base</p>
                  </div>
                </div>
                <Link
                  href={`/dashboard/kb/${kb.id}`}
                  className="inline-flex items-center px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                >
                  <Settings className="w-4 h-4 mr-1" />
                  Settings
                </Link>
              </div>

              {kb.description && (
                <p className="text-gray-600 text-sm mb-4">{kb.description}</p>
              )}

              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Knowledge Chunks:</span>
                  <span className="font-medium">{kb._count.knowledgeChunks}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Assigned to:</span>
                  <span className="font-medium">
                    {kb.assignedChatbot ? (
                      <Link
                        href={`/dashboard/chatbots/${kb.assignedChatbot.id}`}
                        className="text-blue-600 hover:text-blue-800 inline-flex items-center"
                      >
                        {kb.assignedChatbot.approvedDomain}
                        <ExternalLink className="w-3 h-3 ml-1" />
                      </Link>
                    ) : (
                      <span className="text-gray-500">Not assigned</span>
                    )}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Create KB Modal */}
      {showCreateModal && (
        <CreateKBModal
          planKbType={planKbType}
          onClose={() => setShowCreateModal(false)}
          onSuccess={() => {
            setShowCreateModal(false)
            router.refresh()
          }}
        />
      )}
    </div>
  )
}

// Create KB Modal Component
function CreateKBModal({
  planKbType,
  onClose,
  onSuccess
}: {
  planKbType: string
  onClose: () => void
  onSuccess: () => void
}) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    kbType: 'simple'
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    try {
      const response = await fetch('/api/user/knowledge-bases', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create knowledge base')
      }

      onSuccess()
    } catch (error) {
      console.error('Error creating knowledge base:', error)
      setError(error instanceof Error ? error.message : 'Failed to create knowledge base')
    } finally {
      setIsLoading(false)
    }
  }

  // Determine available KB types based on plan
  const availableKbTypes = planKbType === 'structured' 
    ? [{ value: 'simple', label: 'Simple' }, { value: 'structured', label: 'Structured' }]
    : [{ value: 'simple', label: 'Simple' }]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-md w-full p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Create Knowledge Base</h3>
        
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
            <p className="text-sm text-red-800">{error}</p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
              placeholder="My Knowledge Base"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
              placeholder="Optional description..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Type *
            </label>
            <select
              value={formData.kbType}
              onChange={(e) => setFormData(prev => ({ ...prev, kbType: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              {availableKbTypes.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
            <p className="text-xs text-gray-500 mt-1">
              {planKbType === 'structured' 
                ? 'You can create both simple and structured knowledge bases'
                : 'Your plan only supports simple knowledge bases'
              }
            </p>
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="flex-1 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 transition-colors"
            >
              {isLoading ? 'Creating...' : 'Create'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
