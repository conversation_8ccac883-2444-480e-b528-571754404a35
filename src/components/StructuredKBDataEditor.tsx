"use client"

import { useState } from 'react'
import { Layers, AlertCircle, ArrowLeft } from 'lucide-react'
import { useRouter } from 'next/navigation'

interface KnowledgeBase {
  id: string
  name: string
  kbType: string
  structuredKbBrand: any
  structuredKbContact: any
  structuredKbTeachers: any[]
  structuredKbTtcs: any[]
  structuredKbRetreats: any[]
  structuredKbPolicies: any
  structuredKbFaqs: any[]
  _count: {
    knowledgeChunks: number
  }
}

interface StructuredKBDataEditorProps {
  knowledgeBase: KnowledgeBase
}

export default function StructuredKBDataEditor({ knowledgeBase }: StructuredKBDataEditorProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <Layers className="w-6 h-6 text-green-600" />
            <h3 className="text-xl font-semibold text-gray-900">Structured Data Editor</h3>
          </div>
        </div>

        {/* Coming Soon Message */}
        <div className="text-center py-12">
          <Layers className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">Structured Data Editor</h3>
          <p className="text-gray-600 mb-6 max-w-md mx-auto">
            The structured knowledge base editor with forms for school info, teachers, TTCs, retreats, 
            policies, and FAQs will be available in the next development phase.
          </p>
          
          {/* Current Data Summary */}
          <div className="bg-gray-50 rounded-lg p-4 max-w-md mx-auto">
            <h4 className="text-sm font-medium text-gray-900 mb-3">Current Data Summary</h4>
            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex justify-between">
                <span>School Brand:</span>
                <span>{knowledgeBase.structuredKbBrand ? 'Configured' : 'Not set'}</span>
              </div>
              <div className="flex justify-between">
                <span>Contact Info:</span>
                <span>{knowledgeBase.structuredKbContact ? 'Configured' : 'Not set'}</span>
              </div>
              <div className="flex justify-between">
                <span>Teachers:</span>
                <span>{knowledgeBase.structuredKbTeachers?.length || 0}</span>
              </div>
              <div className="flex justify-between">
                <span>TTCs:</span>
                <span>{knowledgeBase.structuredKbTtcs?.length || 0}</span>
              </div>
              <div className="flex justify-between">
                <span>Retreats:</span>
                <span>{knowledgeBase.structuredKbRetreats?.length || 0}</span>
              </div>
              <div className="flex justify-between">
                <span>Policies:</span>
                <span>{knowledgeBase.structuredKbPolicies ? 'Configured' : 'Not set'}</span>
              </div>
              <div className="flex justify-between">
                <span>FAQs:</span>
                <span>{knowledgeBase.structuredKbFaqs?.length || 0}</span>
              </div>
              <div className="flex justify-between border-t pt-2 mt-2">
                <span>Processed Chunks:</span>
                <span>{knowledgeBase._count.knowledgeChunks}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-between items-center">
        <button
          onClick={() => router.back()}
          className="inline-flex items-center px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to KB Settings
        </button>

        <div className="text-sm text-gray-500">
          Structured editor coming in Sprint 3
        </div>
      </div>

      {/* Development Note */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-3">
          <AlertCircle className="w-5 h-5 text-blue-600 mt-0.5" />
          <div>
            <h4 className="text-sm font-medium text-blue-900">Development Status</h4>
            <p className="text-sm text-blue-800 mt-1">
              The structured knowledge base editor is planned for Sprint 3. It will include comprehensive 
              forms for managing school information, teacher profiles, TTC programs, retreats, policies, 
              and frequently asked questions.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
