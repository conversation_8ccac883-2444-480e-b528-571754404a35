"use client"

import { useState } from 'react'
import { Settings, Save, Edit, Plus, Database, Key, MessageSquare } from 'lucide-react'
import { useRouter } from 'next/navigation'

interface SystemConfig {
  id: string
  key: string
  value: string
  description: string | null
  createdAt: Date | string
  updatedAt: Date | string
}

interface AdminSettingsClientProps {
  systemConfigs: SystemConfig[]
}

export default function AdminSettingsClient({ systemConfigs }: AdminSettingsClientProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [editingConfig, setEditingConfig] = useState<string | null>(null)
  const [showCreateModal, setShowCreateModal] = useState(false)

  // Find specific configs
  const defaultSystemPrompt = systemConfigs.find(config => config.key === 'default_system_prompt')

  const handleUpdateConfig = async (key: string, value: string, description?: string) => {
    setIsLoading(true)
    try {
      const response = await fetch('/api/admin/system-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          key,
          value,
          description
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update configuration')
      }

      setEditingConfig(null)
      router.refresh()
    } catch (error) {
      console.error('Error updating config:', error)
      alert('Failed to update configuration. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-8">
      {/* System Prompt Configuration */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <MessageSquare className="w-6 h-6 text-blue-600" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Default System Prompt</h2>
              <p className="text-sm text-gray-600">
                The global system prompt used by all chatbots unless overridden by users with custom permissions
              </p>
            </div>
          </div>
        </div>
        
        <div className="p-6">
          <SystemPromptEditor 
            config={defaultSystemPrompt}
            onUpdate={handleUpdateConfig}
            isLoading={isLoading}
          />
        </div>
      </div>

      {/* LLM Provider Configuration */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Key className="w-6 h-6 text-green-600" />
              <div>
                <h2 className="text-xl font-semibold text-gray-900">LLM Provider Settings</h2>
                <p className="text-sm text-gray-600">
                  Configure default LLM providers and API keys
                </p>
              </div>
            </div>
            <button
              onClick={() => setShowCreateModal(true)}
              className="inline-flex items-center px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Provider
            </button>
          </div>
        </div>
        
        <div className="p-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p className="text-sm text-blue-800">
              <strong>Coming Soon:</strong> LLM provider management will be available in a future update. 
              Currently, only Gemini is supported with environment variable configuration.
            </p>
          </div>
        </div>
      </div>

      {/* All System Configurations */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <Database className="w-6 h-6 text-purple-600" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">All System Configurations</h2>
              <p className="text-sm text-gray-600">
                View and manage all system configuration values
              </p>
            </div>
          </div>
        </div>
        
        <div className="p-6">
          {systemConfigs.length === 0 ? (
            <p className="text-gray-500 text-center py-8">No system configurations found</p>
          ) : (
            <div className="space-y-4">
              {systemConfigs.map((config) => (
                <div key={config.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">{config.key}</h3>
                      {config.description && (
                        <p className="text-sm text-gray-600 mt-1">{config.description}</p>
                      )}
                      <div className="mt-2">
                        {config.key === 'default_system_prompt' ? (
                          <p className="text-sm text-gray-500">
                            {config.value.length > 100 
                              ? `${config.value.substring(0, 100)}...` 
                              : config.value
                            }
                          </p>
                        ) : (
                          <p className="text-sm font-mono bg-gray-50 p-2 rounded border">
                            {config.value}
                          </p>
                        )}
                      </div>
                    </div>
                    <button
                      onClick={() => setEditingConfig(config.id)}
                      className="ml-4 inline-flex items-center px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                    >
                      <Edit className="w-3 h-3 mr-1" />
                      Edit
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// System Prompt Editor Component
function SystemPromptEditor({ 
  config, 
  onUpdate, 
  isLoading 
}: { 
  config?: SystemConfig
  onUpdate: (key: string, value: string, description?: string) => Promise<void>
  isLoading: boolean 
}) {
  const [value, setValue] = useState(config?.value || '')
  const [isEditing, setIsEditing] = useState(false)

  const handleSave = async () => {
    if (!value.trim()) {
      alert('System prompt cannot be empty')
      return
    }

    await onUpdate(
      'default_system_prompt', 
      value.trim(),
      'Default system prompt used by all chatbots unless overridden by users with custom prompt permissions'
    )
    setIsEditing(false)
  }

  const handleCancel = () => {
    setValue(config?.value || '')
    setIsEditing(false)
  }

  if (!config) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 mb-4">No default system prompt configured</p>
        <button
          onClick={() => setIsEditing(true)}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="w-4 h-4 mr-2" />
          Create Default System Prompt
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {isEditing ? (
        <>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Default System Prompt
            </label>
            <textarea
              value={value}
              onChange={(e) => setValue(e.target.value)}
              rows={12}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter the default system prompt..."
            />
            <p className="text-sm text-gray-500 mt-1">
              This prompt will be used by all chatbots unless users have custom prompt permissions enabled.
            </p>
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={handleSave}
              disabled={isLoading || !value.trim()}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              <Save className="w-4 h-4 mr-2" />
              {isLoading ? 'Saving...' : 'Save'}
            </button>
            <button
              onClick={handleCancel}
              disabled={isLoading}
              className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:opacity-50 transition-colors"
            >
              Cancel
            </button>
          </div>
        </>
      ) : (
        <>
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <pre className="whitespace-pre-wrap text-sm text-gray-800 font-mono">
              {config.value}
            </pre>
          </div>
          
          <div className="flex justify-between items-center">
            <p className="text-sm text-gray-500">
              Last updated: {new Date(config.updatedAt).toLocaleString()}
            </p>
            <button
              onClick={() => {
                setValue(config.value)
                setIsEditing(true)
              }}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Edit className="w-4 h-4 mr-2" />
              Edit System Prompt
            </button>
          </div>
        </>
      )}
    </div>
  )
}
