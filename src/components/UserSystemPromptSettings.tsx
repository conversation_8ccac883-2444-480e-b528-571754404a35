"use client"

import { useState } from 'react'
import { Save, MessageSquare, User, Setting<PERSON> } from 'lucide-react'
import { useRouter } from 'next/navigation'

interface User {
  id: string
  email: string
  name: string | null
  canCustomizeSystemPrompt: boolean
  useCustomSystemPrompt: boolean
  customSystemPrompt: string | null
  simpleKbCharacterLimit: number | null
  subscription: {
    plan: {
      name: string
      features: any
    }
  } | null
}

interface UserSystemPromptSettingsProps {
  user: User
}

export default function UserSystemPromptSettings({ user }: UserSystemPromptSettingsProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    canCustomizeSystemPrompt: user.canCustomizeSystemPrompt,
    useCustomSystemPrompt: user.useCustomSystemPrompt,
    customSystemPrompt: user.customSystemPrompt || '',
    simpleKbCharacterLimit: user.simpleKbCharacterLimit || ''
  })

  const defaultCharacterLimit = user.subscription?.plan?.features?.simpleKbCharacterLimit || 10000

  const handleSave = async () => {
    setIsLoading(true)
    try {
      // Update all user settings in one call
      const response = await fetch(`/api/admin/users/${user.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          canCustomizeSystemPrompt: formData.canCustomizeSystemPrompt,
          useCustomSystemPrompt: formData.useCustomSystemPrompt,
          customSystemPrompt: formData.customSystemPrompt.trim() || null,
          simpleKbCharacterLimit: formData.simpleKbCharacterLimit ? parseInt(formData.simpleKbCharacterLimit.toString()) : null
        })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update user settings')
      }

      router.refresh()
    } catch (error) {
      console.error('Error updating user settings:', error)
      alert(error instanceof Error ? error.message : 'Failed to update user settings. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* System Prompt Permission */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-4">
          <MessageSquare className="w-6 h-6 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">System Prompt Settings</h3>
        </div>

        <div className="space-y-4">
          <div className="flex items-center space-x-3">
            <input
              type="checkbox"
              id="canCustomizeSystemPrompt"
              checked={formData.canCustomizeSystemPrompt}
              onChange={(e) => setFormData(prev => ({ 
                ...prev, 
                canCustomizeSystemPrompt: e.target.checked,
                // Reset custom prompt settings if permission is disabled
                useCustomSystemPrompt: e.target.checked ? prev.useCustomSystemPrompt : false,
                customSystemPrompt: e.target.checked ? prev.customSystemPrompt : ''
              }))}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="canCustomizeSystemPrompt" className="text-sm font-medium text-gray-700">
              Allow user to customize system prompt
            </label>
          </div>

          {formData.canCustomizeSystemPrompt && (
            <div className="ml-7 space-y-4 border-l-2 border-blue-200 pl-4">
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="useCustomSystemPrompt"
                  checked={formData.useCustomSystemPrompt}
                  onChange={(e) => setFormData(prev => ({ ...prev, useCustomSystemPrompt: e.target.checked }))}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="useCustomSystemPrompt" className="text-sm font-medium text-gray-700">
                  User wants to use custom system prompt
                </label>
              </div>

              {formData.useCustomSystemPrompt && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Custom System Prompt
                  </label>
                  <textarea
                    value={formData.customSystemPrompt}
                    onChange={(e) => setFormData(prev => ({ ...prev, customSystemPrompt: e.target.value }))}
                    rows={6}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter custom system prompt..."
                  />
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Character Limit Override */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-4">
          <Settings className="w-6 h-6 text-green-600" />
          <h3 className="text-lg font-semibold text-gray-900">Simple KB Character Limit</h3>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Character Limit Override
            </label>
            <input
              type="number"
              value={formData.simpleKbCharacterLimit}
              onChange={(e) => setFormData(prev => ({ ...prev, simpleKbCharacterLimit: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
              placeholder={`Default: ${defaultCharacterLimit.toLocaleString()}`}
              min="1000"
            />
            <p className="text-sm text-gray-500 mt-1">
              Leave empty to use plan default ({defaultCharacterLimit.toLocaleString()} characters). 
              Set a custom limit to override the plan default for this user.
            </p>
          </div>
        </div>
      </div>

      {/* User Info */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-center space-x-3 mb-2">
          <User className="w-5 h-5 text-gray-600" />
          <h4 className="text-sm font-medium text-gray-900">User Information</h4>
        </div>
        <div className="text-sm text-gray-600 space-y-1">
          <p><strong>Email:</strong> {user.email}</p>
          <p><strong>Name:</strong> {user.name || 'Not set'}</p>
          <p><strong>Plan:</strong> {user.subscription?.plan?.name || 'No subscription'}</p>
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-end">
        <button
          onClick={handleSave}
          disabled={isLoading}
          className="inline-flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
        >
          <Save className="w-4 h-4 mr-2" />
          {isLoading ? 'Saving...' : 'Save Settings'}
        </button>
      </div>
    </div>
  )
}
