import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import bcrypt from "bcryptjs"
import { prisma } from "@/lib/prisma"
import { sendWelcomeEmail } from "@/lib/email-service"

const signupSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  planId: z.string().min(1, "Plan selection is required"),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = signupSchema.parse(body)

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: validatedData.email }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: "User with this email already exists" },
        { status: 400 }
      )
    }

    // Verify the plan exists and is active
    const plan = await prisma.plan.findFirst({
      where: {
        id: validatedData.planId,
        isActive: true
      }
    })

    if (!plan) {
      return NextResponse.json(
        { error: "Invalid or inactive plan selected" },
        { status: 400 }
      )
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash(validatedData.password, 12)

    // Create user with subscription in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create the user
      const user = await tx.user.create({
        data: {
          name: validatedData.name,
          email: validatedData.email,
          password: hashedPassword,
          role: 'USER'
        }
      })

      // Create the subscription (initially inactive until payment)
      const subscription = await tx.subscription.create({
        data: {
          userId: user.id,
          planId: validatedData.planId,
          status: 'created', // Will be updated to 'active' after successful payment
          currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
          tokensUsedThisPeriod: 0,
          sessionsThisPeriod: 0
        }
      })

      return { user, subscription }
    })

    // Send welcome email (don't block the response if it fails)
    try {
      await sendWelcomeEmail(result.user.email, {
        userName: result.user.name || 'User',
        planName: plan.name,
        loginUrl: `${process.env.NEXTAUTH_URL}/login`
      })
    } catch (emailError) {
      console.error('Failed to send welcome email:', emailError)
      // Don't fail the signup if email fails
    }

    return NextResponse.json({
      success: true,
      userId: result.user.id,
      message: "Account created successfully"
    })

  } catch (error) {
    console.error("Signup error:", error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Failed to create account" },
      { status: 500 }
    )
  }
}
