import { NextRequest, NextResponse } from "next/server"
import { verifyUserWithRateLimit } from "@/lib/enhanced-auth"
import { prisma } from "@/lib/prisma"

// GET /api/user/leads - Get all leads for user's chatbots
export async function GET(request: NextRequest) {
  try {
    const { user } = await verifyUserWithRateLimit(request)
    const { searchParams } = new URL(request.url)
    const format = searchParams.get('format') // 'csv' for CSV export

    // Get all chatbots for this user
    const userChatbots = await prisma.chatbot.findMany({
      where: { userId: user.id },
      select: { id: true, approvedDomain: true }
    })

    if (userChatbots.length === 0) {
      return NextResponse.json([])
    }

    const chatbotIds = userChatbots.map(bot => bot.id)

    // Get all visitors who have had chat sessions with user's chatbots
    const visitors = await prisma.visitor.findMany({
      where: {
        chatSessions: {
          some: {
            chatbotId: {
              in: chatbotIds
            }
          }
        }
      },
      include: {
        chatSessions: {
          where: {
            chatbotId: {
              in: chatbotIds
            }
          },
          include: {
            chatbot: {
              select: {
                id: true,
                approvedDomain: true
              }
            },
            messages: {
              select: {
                id: true,
                senderType: true,
                createdAt: true
              }
            }
          },
          orderBy: {
            createdAt: 'asc'
          }
        }
      }
    })

    // Process visitors into leads format
    const leads = visitors
      .filter(visitor => visitor.email || visitor.name) // Only include visitors with contact info
      .map(visitor => {
        const firstSession = visitor.chatSessions[0]
        const lastSession = visitor.chatSessions[visitor.chatSessions.length - 1]
        const totalMessages = visitor.chatSessions.reduce((sum, session) => sum + session.messages.length, 0)
        const totalSessions = visitor.chatSessions.length

        // Extract additional info from profileData
        const profileData = visitor.profileData as any || {}
        
        return {
          id: visitor.id,
          name: visitor.name || 'Anonymous',
          email: visitor.email || '',
          phone: profileData.phone || '',
          company: profileData.company || '',
          interests: profileData.interests || [],
          source: firstSession?.chatbot.approvedDomain || 'Unknown',
          firstSeenAt: firstSession?.createdAt || new Date(),
          lastSeenAt: lastSession?.createdAt || new Date(),
          totalSessions,
          totalMessages,
          status: this.determineLeadStatus(visitor.chatSessions),
          profileData: profileData
        }
      })
      .sort((a, b) => new Date(b.lastSeenAt).getTime() - new Date(a.lastSeenAt).getTime())

    // Return CSV format if requested
    if (format === 'csv') {
      const csvHeaders = [
        'Name',
        'Email', 
        'Phone',
        'Company',
        'Source',
        'First Seen',
        'Last Seen',
        'Total Sessions',
        'Total Messages',
        'Status'
      ]

      const csvRows = leads.map(lead => [
        lead.name,
        lead.email,
        lead.phone,
        lead.company,
        lead.source,
        lead.firstSeenAt.toISOString().split('T')[0],
        lead.lastSeenAt.toISOString().split('T')[0],
        lead.totalSessions.toString(),
        lead.totalMessages.toString(),
        lead.status
      ])

      const csvContent = [
        csvHeaders.join(','),
        ...csvRows.map(row => row.map(field => `"${field}"`).join(','))
      ].join('\n')

      return new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': 'attachment; filename="leads.csv"'
        }
      })
    }

    return NextResponse.json(leads)

  } catch (error) {
    console.error("Error fetching leads:", error)
    return NextResponse.json({ error: "Failed to fetch leads" }, { status: 500 })
  }
}

// Helper function to determine lead status based on chat sessions
function determineLeadStatus(chatSessions: any[]): string {
  if (chatSessions.length === 0) return 'New'
  
  const lastSession = chatSessions[chatSessions.length - 1]
  const daysSinceLastContact = Math.floor(
    (Date.now() - new Date(lastSession.createdAt).getTime()) / (1000 * 60 * 60 * 24)
  )

  if (daysSinceLastContact === 0) return 'Active'
  if (daysSinceLastContact <= 7) return 'Recent'
  if (daysSinceLastContact <= 30) return 'Warm'
  return 'Cold'
}

// GET /api/user/leads/stats - Get lead statistics
export async function POST(request: NextRequest) {
  try {
    const { user } = await verifyUserWithRateLimit(request)

    // Get all chatbots for this user
    const userChatbots = await prisma.chatbot.findMany({
      where: { userId: user.id },
      select: { id: true }
    })

    if (userChatbots.length === 0) {
      return NextResponse.json({
        totalLeads: 0,
        thisWeek: 0,
        thisMonth: 0,
        conversionRate: 0,
        avgResponseTime: 0
      })
    }

    const chatbotIds = userChatbots.map(bot => bot.id)
    const now = new Date()
    const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)

    // Get total leads (visitors with contact info)
    const totalLeads = await prisma.visitor.count({
      where: {
        AND: [
          {
            OR: [
              { email: { not: null } },
              { name: { not: null } }
            ]
          },
          {
            chatSessions: {
              some: {
                chatbotId: { in: chatbotIds }
              }
            }
          }
        ]
      }
    })

    // Get leads from this week
    const thisWeekLeads = await prisma.visitor.count({
      where: {
        AND: [
          {
            OR: [
              { email: { not: null } },
              { name: { not: null } }
            ]
          },
          {
            chatSessions: {
              some: {
                chatbotId: { in: chatbotIds },
                createdAt: { gte: weekAgo }
              }
            }
          }
        ]
      }
    })

    // Get leads from this month
    const thisMonthLeads = await prisma.visitor.count({
      where: {
        AND: [
          {
            OR: [
              { email: { not: null } },
              { name: { not: null } }
            ]
          },
          {
            chatSessions: {
              some: {
                chatbotId: { in: chatbotIds },
                createdAt: { gte: monthAgo }
              }
            }
          }
        ]
      }
    })

    // Calculate basic conversion rate (leads vs total visitors)
    const totalVisitors = await prisma.visitor.count({
      where: {
        chatSessions: {
          some: {
            chatbotId: { in: chatbotIds }
          }
        }
      }
    })

    const conversionRate = totalVisitors > 0 ? Math.round((totalLeads / totalVisitors) * 100) : 0

    return NextResponse.json({
      totalLeads,
      thisWeek: thisWeekLeads,
      thisMonth: thisMonthLeads,
      conversionRate,
      avgResponseTime: 4.2 // Placeholder - would need more complex calculation
    })

  } catch (error) {
    console.error("Error fetching lead stats:", error)
    return NextResponse.json({ error: "Failed to fetch lead statistics" }, { status: 500 })
  }
}
