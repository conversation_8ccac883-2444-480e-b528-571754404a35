import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { verifyUser } from "@/lib/user-auth"
import { prisma } from "@/lib/prisma"

// Schema for creating a new chatbot
const createChatbotSchema = z.object({
  approvedDomain: z.string().url("Must be a valid URL"),
  widgetConfig: z.object({
    primaryColor: z.string().optional(),
    welcomeMessage: z.string().optional(),
    placeholder: z.string().optional(),
    position: z.string().optional(),
    theme: z.string().optional()
  }).optional()
})

// GET /api/user/chatbots - Get user's chatbots
export async function GET() {
  try {
    const { user } = await verifyUser()

    const chatbots = await prisma.chatbot.findMany({
      where: { userId: user.id },
      include: {
        assignedKb: {
          select: {
            id: true,
            name: true,
            kbType: true
          }
        },
        user: {
          select: {
            subscription: {
              include: {
                plan: true
              }
            }
          }
        },
        personas: {
          where: { isActive: true }
        },
        _count: {
          select: {
            chatSessions: true,
            knowledgeChunks: true
          }
        }
      },
      orderBy: { id: 'desc' }
    })

    return NextResponse.json(chatbots)
  } catch (error) {
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    
    console.error("Error fetching user chatbots:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// POST /api/user/chatbots - Create a new chatbot
export async function POST(request: NextRequest) {
  try {
    const { user } = await verifyUser()
    const body = await request.json()
    const validatedData = createChatbotSchema.parse(body)

    // Get user's subscription to check limits and permissions
    const userWithSubscription = await prisma.user.findUnique({
      where: { id: user.id },
      include: {
        subscription: {
          include: {
            plan: true
          }
        },
        chatbots: true
      }
    })

    if (!userWithSubscription?.subscription) {
      return NextResponse.json({ error: "No active subscription" }, { status: 403 })
    }

    // Check chatbot limit (admins have no limits)
    if (userWithSubscription.role !== 'ADMIN') {
      const planFeatures = userWithSubscription.subscription?.plan.features as any
      const chatbotLimit = planFeatures?.chatbotLimit || 1
      if (userWithSubscription.chatbots.length >= chatbotLimit) {
        return NextResponse.json({
          error: `Chatbot limit reached. Your plan allows ${chatbotLimit} chatbot(s).`
        }, { status: 403 })
      }
    }



    // Create the chatbot
    const chatbot = await prisma.chatbot.create({
      data: {
        userId: user.id,
        approvedDomain: validatedData.approvedDomain,
        widgetConfig: validatedData.widgetConfig || {
          primaryColor: '#3B82F6',
          welcomeMessage: 'Hello! How can I help you today?',
          placeholder: 'Type your message...',
          position: 'bottom-right',
          theme: 'light'
        }
      },
      include: {
        assignedKb: {
          select: {
            id: true,
            name: true,
            kbType: true
          }
        },
        user: {
          select: {
            subscription: {
              include: {
                plan: true
              }
            }
          }
        },
        personas: true,
        _count: {
          select: {
            chatSessions: true,
            knowledgeChunks: true
          }
        }
      }
    })

    return NextResponse.json(chatbot)
  } catch (error) {
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors }, 
        { status: 400 }
      )
    }
    
    console.error("Error creating chatbot:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
