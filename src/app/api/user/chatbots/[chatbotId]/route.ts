import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { verifyUserWithRateLimit, validateResourceOwnership, handleRouteError, RateLimits } from "@/lib/enhanced-auth"
import { prisma } from "@/lib/prisma"

// Schema for updating chatbot
const updateChatbotSchema = z.object({
  approvedDomain: z.string().url("Must be a valid URL").optional(),
  widgetConfig: z.object({
    primaryColor: z.string().optional(),
    welcomeMessage: z.string().optional(),
    placeholder: z.string().optional(),
    position: z.string().optional(),
    theme: z.string().optional()
  }).optional()
})

// GET /api/user/chatbots/[chatbotId] - Get specific chatbot
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ chatbotId: string }> }
) {
  try {
    const { user } = await verifyUserWithRateLimit(request, RateLimits.NORMAL)
    const { chatbotId } = await params
    
    // Validate user owns this chatbot
    await validateResourceOwnership(user.id, chatbotId, 'chatbot')

    const chatbot = await prisma.chatbot.findFirst({
      where: { 
        id: chatbotId,
        userId: user.id // Ensure user owns this chatbot
      },
      include: {
        assignedKb: {
          select: {
            id: true,
            name: true,
            kbType: true
          }
        },
        user: {
          select: {
            subscription: {
              include: {
                plan: true
              }
            }
          }
        },
        personas: {
          where: { isActive: true }
        },
        _count: {
          select: {
            chatSessions: true,
            knowledgeChunks: true
          }
        }
      }
    })

    if (!chatbot) {
      return NextResponse.json({ error: "Chatbot not found" }, { status: 404 })
    }

    return NextResponse.json(chatbot)
  } catch (error) {
    return handleRouteError(error)
  }
}

// PUT /api/user/chatbots/[chatbotId] - Update chatbot
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ chatbotId: string }> }
) {
  try {
    const { user } = await verifyUserWithRateLimit(request, RateLimits.NORMAL)
    const { chatbotId } = await params
    
    // Validate user owns this chatbot
    await validateResourceOwnership(user.id, chatbotId, 'chatbot')
    const body = await request.json()
    const validatedData = updateChatbotSchema.parse(body)

    // Check if chatbot exists and belongs to user
    const existingChatbot = await prisma.chatbot.findFirst({
      where: {
        id: chatbotId,
        userId: user.id
      }
    })

    if (!existingChatbot) {
      return NextResponse.json({ error: "Chatbot not found" }, { status: 404 })
    }

    // Update chatbot
    const updatedChatbot = await prisma.chatbot.update({
      where: { id: chatbotId },
      data: validatedData,
      include: {
        assignedKb: {
          select: {
            id: true,
            name: true,
            kbType: true
          }
        },
        user: {
          select: {
            subscription: {
              include: {
                plan: true
              }
            }
          }
        },
        personas: {
          where: { isActive: true }
        },
        _count: {
          select: {
            chatSessions: true,
            knowledgeChunks: true
          }
        }
      }
    })

    return NextResponse.json(updatedChatbot)
  } catch (error) {
    return handleRouteError(error)
  }
}

// DELETE /api/user/chatbots/[chatbotId] - Delete chatbot
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ chatbotId: string }> }
) {
  try {
    const { user } = await verifyUserWithRateLimit(request, RateLimits.NORMAL)
    const { chatbotId } = await params
    
    // Validate user owns this chatbot
    await validateResourceOwnership(user.id, chatbotId, 'chatbot')

    // Check if chatbot exists and belongs to user
    const existingChatbot = await prisma.chatbot.findFirst({
      where: { 
        id: chatbotId,
        userId: user.id
      },
      include: {
        _count: {
          select: {
            chatSessions: true
          }
        }
      }
    })

    if (!existingChatbot) {
      return NextResponse.json({ error: "Chatbot not found" }, { status: 404 })
    }

    // Delete chatbot (this will cascade delete related data)
    await prisma.chatbot.delete({
      where: { id: chatbotId }
    })

    return NextResponse.json({ 
      message: "Chatbot deleted successfully",
      deletedSessions: existingChatbot._count.chatSessions
    })
  } catch (error) {
    return handleRouteError(error)
  }
}
