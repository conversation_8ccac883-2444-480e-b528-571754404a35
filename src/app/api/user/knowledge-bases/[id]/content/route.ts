import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

// Schema for updating KB content
const updateContentSchema = z.object({
  simpleKbText: z.string().optional(),
  structuredData: z.any().optional() // For structured KB updates
})

// PUT /api/user/knowledge-bases/[id]/content - Update KB content
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get user session
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }

    const { id } = await params
    const body = await request.json()
    const validatedData = updateContentSchema.parse(body)

    // Check if K<PERSON> exists and belongs to user
    const existingKB = await prisma.knowledgeBase.findFirst({
      where: { 
        id,
        userId: session.user.id
      },
      include: {
        user: {
          include: {
            subscription: {
              include: {
                plan: true
              }
            }
          }
        }
      }
    })

    if (!existingKB) {
      return NextResponse.json({ error: "Knowledge base not found" }, { status: 404 })
    }

    // For simple KB, check character limits
    if (existingKB.kbType === 'simple' && validatedData.simpleKbText !== undefined) {
      const user = existingKB.user
      let characterLimit = 10000 // Default limit

      // Admin can have no limits
      if (user.role === 'ADMIN') {
        characterLimit = Infinity
      } else if (user.simpleKbCharacterLimit) {
        // Admin override for this user
        characterLimit = user.simpleKbCharacterLimit
      } else if (user.subscription?.plan) {
        // Plan-based limit
        const planFeatures = user.subscription.plan.features as any
        characterLimit = planFeatures?.simpleKbCharacterLimit || 10000
      }

      if (validatedData.simpleKbText.length > characterLimit) {
        return NextResponse.json({ 
          error: `Content exceeds the character limit of ${characterLimit.toLocaleString()} characters.` 
        }, { status: 400 })
      }
    }

    // Update the KB content and mark as needing reprocessing
    const updatedKB = await prisma.knowledgeBase.update({
      where: { id },
      data: {
        ...(validatedData.simpleKbText !== undefined && { simpleKbText: validatedData.simpleKbText }),
        ...(validatedData.structuredData !== undefined && { structuredData: validatedData.structuredData }),
        needsReprocessing: true, // Mark as needing reprocessing
        updatedAt: new Date()
      }
    })

    return NextResponse.json(updatedKB)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors }, 
        { status: 400 }
      )
    }
    
    console.error("Error updating KB content:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
