import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { verifyUser } from "@/lib/user-auth"
import { prisma } from "@/lib/prisma"

// Schema for updating knowledge base
const updateKbSchema = z.object({
  name: z.string().min(1, "Knowledge base name is required").optional(),
  description: z.string().optional(),
  assignedChatbotId: z.string().nullable().optional()
})

// GET /api/user/knowledge-bases/[id] - Get specific knowledge base
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { user } = await verifyUser()
    const { id: kbId } = await params

    const knowledgeBase = await prisma.knowledgeBase.findFirst({
      where: { 
        id: kbId,
        userId: user.id // Ensure user owns this KB
      },
      include: {
        assignedChatbot: {
          select: {
            id: true,
            approvedDomain: true
          }
        },
        _count: {
          select: {
            knowledgeChunks: true
          }
        }
      }
    })

    if (!knowledgeBase) {
      return NextResponse.json({ error: "Knowledge base not found" }, { status: 404 })
    }

    return NextResponse.json(knowledgeBase)
  } catch (error) {
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    
    console.error("Error fetching knowledge base:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// PUT /api/user/knowledge-bases/[id] - Update knowledge base
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { user } = await verifyUser()
    const { id: kbId } = await params
    const body = await request.json()
    const validatedData = updateKbSchema.parse(body)

    // Check if KB exists and belongs to user
    const existingKb = await prisma.knowledgeBase.findFirst({
      where: {
        id: kbId,
        userId: user.id
      },
      include: {
        assignedChatbot: true
      }
    })

    if (!existingKb) {
      return NextResponse.json({ error: "Knowledge base not found" }, { status: 404 })
    }

    // If assigning to a chatbot, validate the chatbot belongs to the user
    if (validatedData.assignedChatbotId) {
      const chatbot = await prisma.chatbot.findFirst({
        where: {
          id: validatedData.assignedChatbotId,
          userId: user.id
        }
      })

      if (!chatbot) {
        return NextResponse.json({ error: "Chatbot not found or not owned by user" }, { status: 404 })
      }

      // Check if chatbot already has a KB assigned
      const existingAssignment = await prisma.chatbot.findFirst({
        where: {
          id: validatedData.assignedChatbotId,
          assignedKbId: { not: null }
        }
      })

      if (existingAssignment && existingAssignment.assignedKbId !== kbId) {
        return NextResponse.json({ 
          error: "This chatbot already has a knowledge base assigned. Please unassign it first." 
        }, { status: 400 })
      }
    }

    // Update KB and chatbot assignment in a transaction
    const updatedKb = await prisma.$transaction(async (tx) => {
      // If there was a previous assignment, clear it
      if (existingKb.assignedChatbot) {
        await tx.chatbot.update({
          where: { id: existingKb.assignedChatbot.id },
          data: { assignedKbId: null }
        })
      }

      // Update the KB
      const kb = await tx.knowledgeBase.update({
        where: { id: kbId },
        data: {
          name: validatedData.name,
          description: validatedData.description
        },
        include: {
          assignedChatbot: {
            select: {
              id: true,
              approvedDomain: true
            }
          },
          _count: {
            select: {
              knowledgeChunks: true
            }
          }
        }
      })

      // If assigning to a new chatbot, update the chatbot
      if (validatedData.assignedChatbotId) {
        await tx.chatbot.update({
          where: { id: validatedData.assignedChatbotId },
          data: { assignedKbId: kbId }
        })
      }

      return kb
    })

    return NextResponse.json(updatedKb)
  } catch (error) {
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors }, 
        { status: 400 }
      )
    }
    
    console.error("Error updating knowledge base:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// DELETE /api/user/knowledge-bases/[id] - Delete knowledge base
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { user } = await verifyUser()
    const { id: kbId } = await params

    // Check if KB exists and belongs to user
    const existingKb = await prisma.knowledgeBase.findFirst({
      where: { 
        id: kbId,
        userId: user.id
      },
      include: {
        assignedChatbot: true
      }
    })

    if (!existingKb) {
      return NextResponse.json({ error: "Knowledge base not found" }, { status: 404 })
    }

    // Delete KB and clear chatbot assignment in a transaction
    await prisma.$transaction(async (tx) => {
      // If assigned to a chatbot, clear the assignment
      if (existingKb.assignedChatbot) {
        await tx.chatbot.update({
          where: { id: existingKb.assignedChatbot.id },
          data: { assignedKbId: null }
        })
      }

      // Delete the KB (this will cascade delete related data)
      await tx.knowledgeBase.delete({
        where: { id: kbId }
      })
    })

    return NextResponse.json({ 
      message: "Knowledge base deleted successfully"
    })
  } catch (error) {
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    
    console.error("Error deleting knowledge base:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
