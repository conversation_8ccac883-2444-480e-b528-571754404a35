import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

// POST /api/user/knowledge-bases/[id]/embed - Process/embed KB content
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get user session
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }

    const { id } = await params

    // Check if K<PERSON> exists and belongs to user
    const knowledgeBase = await prisma.knowledgeBase.findFirst({
      where: { 
        id,
        userId: session.user.id
      }
    })

    if (!knowledgeBase) {
      return NextResponse.json({ error: "Knowledge base not found" }, { status: 404 })
    }

    // For now, we'll just clear existing chunks and mark as processed
    // In the future, this would implement actual embedding/chunking logic
    
    // Delete existing chunks
    await prisma.kBChunk.deleteMany({
      where: { knowledgeBaseId: id }
    })

    // For simple KB, create basic chunks from the text
    if (knowledgeBase.kbType === 'simple' && knowledgeBase.simpleKbText) {
      // Simple chunking: split by paragraphs or every 500 characters
      const text = knowledgeBase.simpleKbText
      const chunks = []
      
      // Split by double newlines (paragraphs) first
      const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0)
      
      for (const paragraph of paragraphs) {
        if (paragraph.length <= 500) {
          chunks.push(paragraph.trim())
        } else {
          // Split long paragraphs into smaller chunks
          const sentences = paragraph.split(/[.!?]+/).filter(s => s.trim().length > 0)
          let currentChunk = ''
          
          for (const sentence of sentences) {
            if ((currentChunk + sentence).length <= 500) {
              currentChunk += sentence + '. '
            } else {
              if (currentChunk.trim()) {
                chunks.push(currentChunk.trim())
              }
              currentChunk = sentence + '. '
            }
          }
          
          if (currentChunk.trim()) {
            chunks.push(currentChunk.trim())
          }
        }
      }

      // Create chunk records
      for (let i = 0; i < chunks.length; i++) {
        await prisma.kBChunk.create({
          data: {
            knowledgeBaseId: id,
            content: chunks[i],
            source: `Simple KB - Chunk ${i + 1}`
          }
        })
      }
    } else if (knowledgeBase.kbType === 'structured') {
      // For structured KB, this would process the structured data
      // For now, just create a placeholder chunk
      await prisma.kBChunk.create({
        data: {
          knowledgeBaseId: id,
          content: "Structured knowledge base content (processing not yet implemented)",
          source: "Structured KB - Placeholder"
        }
      })
    }

    // Update KB status
    await prisma.knowledgeBase.update({
      where: { id },
      data: {
        needsReprocessing: false,
        lastProcessedAt: new Date()
      }
    })

    // Get updated chunk count
    const chunkCount = await prisma.kBChunk.count({
      where: { knowledgeBaseId: id }
    })

    return NextResponse.json({ 
      message: "Knowledge base processed successfully",
      chunkCount 
    })
  } catch (error) {
    console.error("Error processing knowledge base:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
