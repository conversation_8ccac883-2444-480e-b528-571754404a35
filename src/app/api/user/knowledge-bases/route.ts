import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { verifyUser } from "@/lib/user-auth"
import { prisma } from "@/lib/prisma"

// Schema for creating a new knowledge base
const createKbSchema = z.object({
  name: z.string().min(1, "Knowledge base name is required"),
  description: z.string().optional(),
  kbType: z.enum(["simple", "structured"], {
    errorMap: () => ({ message: "KB type must be either 'simple' or 'structured'" })
  })
})

// GET /api/user/knowledge-bases - Get user's knowledge bases
export async function GET() {
  try {
    const { user } = await verifyUser()

    const knowledgeBases = await prisma.knowledgeBase.findMany({
      where: { userId: user.id },
      include: {
        assignedChatbot: {
          select: {
            id: true,
            approvedDomain: true
          }
        },
        _count: {
          select: {
            knowledgeChunks: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    return NextResponse.json(knowledgeBases)
  } catch (error) {
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    
    console.error("Error fetching user knowledge bases:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// POST /api/user/knowledge-bases - Create a new knowledge base
export async function POST(request: NextRequest) {
  try {
    const { user } = await verifyUser()
    const body = await request.json()
    const validatedData = createKbSchema.parse(body)

    // Get user's subscription to check limits
    const userWithSubscription = await prisma.user.findUnique({
      where: { id: user.id },
      include: {
        subscription: {
          include: {
            plan: true
          }
        },
        knowledgeBases: true
      }
    })

    if (!userWithSubscription?.subscription) {
      return NextResponse.json({ error: "No active subscription" }, { status: 403 })
    }

    // Check KB limit (admins have no limits)
    if (user.role !== 'ADMIN') {
      const planFeatures = userWithSubscription.subscription?.plan.features as any
      const kbLimit = planFeatures?.kbLimit || 1
      if (userWithSubscription.knowledgeBases.length >= kbLimit) {
        return NextResponse.json({
          error: `Knowledge base limit reached. Your plan allows ${kbLimit} knowledge base(s).`
        }, { status: 403 })
      }
    }

    // Check if user can create the requested KB type
    const planFeatures = userWithSubscription.subscription?.plan.features as any
    const planKbType = planFeatures?.kbType || 'simple'
    if (validatedData.kbType === 'structured' && planKbType === 'simple') {
      return NextResponse.json({
        error: "Your plan does not support structured knowledge bases. Please upgrade to access this feature."
      }, { status: 403 })
    }

    // Create the knowledge base
    const knowledgeBase = await prisma.knowledgeBase.create({
      data: {
        userId: user.id,
        name: validatedData.name,
        description: validatedData.description,
        kbType: validatedData.kbType
      },
      include: {
        assignedChatbot: {
          select: {
            id: true,
            approvedDomain: true
          }
        },
        _count: {
          select: {
            knowledgeChunks: true
          }
        }
      }
    })

    return NextResponse.json(knowledgeBase)
  } catch (error) {
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors }, 
        { status: 400 }
      )
    }
    
    console.error("Error creating knowledge base:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
