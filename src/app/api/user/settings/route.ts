import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { verifyUser } from "@/lib/enhanced-auth"
import { prisma } from "@/lib/prisma"
import { encrypt, decrypt, isEncryptionAvailable } from "@/lib/encryption"

const widgetConfigSchema = z.object({
  primaryColor: z.string().min(1),
  welcomeMessage: z.string().min(1),
  placeholder: z.string().min(1),
  position: z.enum(['bottom-right', 'bottom-left', 'top-right', 'top-left']),
  theme: z.enum(['light', 'dark']).default('light')
})

const smtpConfigSchema = z.object({
  host: z.string().min(1),
  port: z.number().min(1).max(65535),
  secure: z.boolean(),
  user: z.string().email(),
  password: z.string().min(1)
}).nullable()

const settingsSchema = z.object({
  widgetConfig: widgetConfigSchema,
  smtpConfig: smtpConfigSchema,
  encryptedLlmApiKey: z.string().nullable()
})

// GET /api/user/settings - Get user settings
export async function GET() {
  try {
    const { user } = await verifyUser()

    // Get user with subscription to check BYOK capability
    const userWithSubscription = await prisma.user.findUnique({
      where: { id: user.id },
      include: {
        subscription: {
          include: {
            plan: true
          }
        }
      }
    })

    if (!userWithSubscription) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Get the first chatbot for widget config (simplified approach)
    const chatbot = await prisma.chatbot.findFirst({
      where: { userId: user.id }
    })

    // Default widget configuration
    const defaultWidgetConfig = {
      primaryColor: '#3B82F6',
      welcomeMessage: 'Hello! How can I help you today?',
      placeholder: 'Type your message...',
      position: 'bottom-right' as const,
      theme: 'light' as const
    }

    // Parse existing widget config or use defaults
    let widgetConfig = defaultWidgetConfig
    if (chatbot?.widgetConfig) {
      try {
        widgetConfig = { ...defaultWidgetConfig, ...(chatbot.widgetConfig as any) }
      } catch (error) {
        console.error('Error parsing widget config:', error)
      }
    }

    // Parse SMTP config if it exists
    let smtpConfig = null
    if (chatbot?.smtpConfig) {
      try {
        const encryptedSmtpConfig = chatbot.smtpConfig as any
        if (isEncryptionAvailable() && encryptedSmtpConfig.password) {
          // Decrypt the password
          const decryptedPassword = decrypt(encryptedSmtpConfig.password)
          smtpConfig = {
            ...encryptedSmtpConfig,
            password: decryptedPassword
          }
        } else {
          smtpConfig = encryptedSmtpConfig
        }
      } catch (error) {
        console.error('Error parsing SMTP config:', error)
      }
    }

    // Get encrypted LLM API key
    let encryptedLlmApiKey = null
    if (chatbot?.encryptedLlmApiKey && isEncryptionAvailable()) {
      try {
        encryptedLlmApiKey = decrypt(chatbot.encryptedLlmApiKey)
      } catch (error) {
        console.error('Error decrypting LLM API key:', error)
      }
    }

    const canUseBYOK = userWithSubscription.subscription?.plan.features?.canUseBYOK || false

    return NextResponse.json({
      widgetConfig,
      smtpConfig,
      encryptedLlmApiKey,
      canUseBYOK
    })

  } catch (error) {
    console.error("Error fetching user settings:", error)
    return NextResponse.json({ error: "Failed to fetch settings" }, { status: 500 })
  }
}

// PUT /api/user/settings - Update user settings
export async function PUT(request: NextRequest) {
  try {
    const { user } = await verifyUser()
    const body = await request.json()
    const validatedData = settingsSchema.parse(body)

    // Get user's first chatbot (simplified approach)
    const chatbot = await prisma.chatbot.findFirst({
      where: { userId: user.id }
    })

    if (!chatbot) {
      return NextResponse.json({ error: "No chatbot found" }, { status: 404 })
    }

    // Prepare SMTP config with encryption
    let encryptedSmtpConfig = null
    if (validatedData.smtpConfig) {
      if (isEncryptionAvailable()) {
        // Encrypt the password
        const encryptedPassword = encrypt(validatedData.smtpConfig.password)
        encryptedSmtpConfig = {
          ...validatedData.smtpConfig,
          password: encryptedPassword
        }
      } else {
        // Store without encryption if encryption is not available
        encryptedSmtpConfig = validatedData.smtpConfig
      }
    }

    // Prepare LLM API key with encryption
    let encryptedLlmApiKey = null
    if (validatedData.encryptedLlmApiKey) {
      if (isEncryptionAvailable()) {
        encryptedLlmApiKey = encrypt(validatedData.encryptedLlmApiKey)
      } else {
        encryptedLlmApiKey = validatedData.encryptedLlmApiKey
      }
    }

    // Update the chatbot with new settings
    await prisma.chatbot.update({
      where: { id: chatbot.id },
      data: {
        widgetConfig: validatedData.widgetConfig,
        smtpConfig: encryptedSmtpConfig,
        encryptedLlmApiKey: encryptedLlmApiKey
      }
    })

    return NextResponse.json({ success: true, message: "Settings updated successfully" })

  } catch (error) {
    console.error("Error updating user settings:", error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }

    return NextResponse.json({ error: "Failed to update settings" }, { status: 500 })
  }
}
