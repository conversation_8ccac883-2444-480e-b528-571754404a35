import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

// GET /api/public/plans - Public endpoint for marketing website
export async function GET() {
  try {
    // Fetch all active plans with only public-facing data
    const plans = await prisma.plan.findMany({
      where: {
        isActive: true
      },
      select: {
        id: true,
        name: true,
        price: true,
        features: true
      },
      orderBy: {
        price: 'asc'
      }
    })

    // Set CORS headers to allow requests from marketing website
    const response = NextResponse.json(plans)
    
    // Configure CORS - in production, replace with your actual marketing domain
    response.headers.set('Access-Control-Allow-Origin', '*')
    response.headers.set('Access-Control-Allow-Methods', 'GET')
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type')
    
    return response
  } catch (error) {
    console.error("Error fetching public plans:", error)
    return NextResponse.json(
      { error: "Failed to fetch plans" }, 
      { status: 500 }
    )
  }
}

// Handle preflight requests for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
