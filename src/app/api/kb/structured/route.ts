import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { verifyUser } from "@/lib/user-auth"
import { prisma } from "@/lib/prisma"

// Validation schemas for structured data
const schoolBrandSchema = z.object({
  schoolName: z.string().min(1, "School name is required"),
  tagline: z.string().optional(),
  schoolType: z.string().optional(),
  yogaStylesTaught: z.array(z.string()).default([]),
  missionStatement: z.string().optional(),
  aboutTheSchool: z.string().optional(),
  founderInfo: z.string().optional()
})

const schoolContactSchema = z.object({
  fullAddress: z.string().optional(),
  googleMapsLink: z.string().optional(),
  howToReach: z.string().optional(),
  primaryPhone: z.string().optional(),
  whatsappNumber: z.string().optional(),
  primaryEmail: z.string().email().optional().or(z.literal("")),
  websiteUrl: z.string().url().optional().or(z.literal("")),
  socialMediaLinks: z.array(z.object({
    platform: z.string(),
    url: z.string().url()
  })).default([])
})

const teacherSchema = z.object({
  name: z.string().min(1, "Teacher name is required"),
  role: z.string().optional(),
  photoUrl: z.string().url().optional().or(z.literal("")),
  bio: z.string().optional(),
  certifications: z.array(z.string()).default([])
})

const ttcSchema = z.object({
  name: z.string().min(1, "TTC name is required"),
  certificationBody: z.string().optional(),
  summary: z.string().optional(),
  duration: z.string().optional(),
  skillLevel: z.string().optional(),
  curriculumDetails: z.string().optional(),
  sampleDailySchedule: z.string().optional(),
  priceOptions: z.array(z.object({
    type: z.string(),
    price: z.number()
  })).default([]),
  inclusions: z.array(z.string()).default([]),
  exclusions: z.array(z.string()).default([]),
  upcomingDates: z.array(z.object({
    start: z.string(),
    end: z.string(),
    status: z.string()
  })).default([]),
  applicationProcess: z.string().optional()
})

const retreatSchema = z.object({
  name: z.string().min(1, "Retreat name is required"),
  theme: z.string().optional(),
  duration: z.string().optional(),
  intendedAudience: z.string().optional(),
  highlights: z.array(z.string()).default([]),
  priceOptions: z.array(z.object({
    type: z.string(),
    price: z.number()
  })).default([]),
  upcomingDates: z.array(z.object({
    start: z.string(),
    end: z.string(),
    status: z.string()
  })).default([])
})

const policySchema = z.object({
  codeOfConduct: z.string().optional(),
  paymentPolicy: z.string().optional(),
  cancellationAndRefundPolicy: z.string().optional()
})

const faqSchema = z.object({
  question: z.string().min(1, "Question is required"),
  answer: z.string().min(1, "Answer is required")
})

const structuredKbSchema = z.object({
  chatbotId: z.string(),
  schoolBrand: schoolBrandSchema.optional(),
  schoolContact: schoolContactSchema.optional(),
  teachers: z.array(teacherSchema).default([]),
  ttcs: z.array(ttcSchema).default([]),
  retreats: z.array(retreatSchema).default([]),
  policies: policySchema.optional(),
  faqs: z.array(faqSchema).default([])
})

export async function POST(request: NextRequest) {
  try {
    // Verify user authentication
    const { user } = await verifyUser()
    
    // Parse and validate request body
    const body = await request.json()
    const validatedData = structuredKbSchema.parse(body)
    const { chatbotId } = validatedData

    // Fetch chatbot and verify ownership
    const chatbot = await prisma.chatbot.findFirst({
      where: { 
        id: chatbotId,
        userId: user.id
      },
      include: {
        user: {
          include: {
            subscription: {
              include: {
                plan: true
              }
            }
          }
        }
      }
    })

    if (!chatbot) {
      return NextResponse.json({ error: "Chatbot not found" }, { status: 404 })
    }

    // Check subscription status
    if (!chatbot.user.subscription || chatbot.user.subscription.status !== 'active') {
      return NextResponse.json({ error: "Active subscription required" }, { status: 403 })
    }

    // Save structured data in a transaction
    await prisma.$transaction(async (tx) => {
      // Save School Brand (upsert since it's unique per chatbot)
      if (validatedData.schoolBrand) {
        await tx.schoolBrand.upsert({
          where: { chatbotId },
          update: validatedData.schoolBrand,
          create: { chatbotId, ...validatedData.schoolBrand }
        })
      }

      // Save School Contact (upsert since it's unique per chatbot)
      if (validatedData.schoolContact) {
        await tx.schoolContact.upsert({
          where: { chatbotId },
          update: validatedData.schoolContact,
          create: { chatbotId, ...validatedData.schoolContact }
        })
      }

      // Save Policies (upsert since it's unique per chatbot)
      if (validatedData.policies) {
        await tx.policy.upsert({
          where: { chatbotId },
          update: validatedData.policies,
          create: { chatbotId, ...validatedData.policies }
        })
      }

      // Delete and recreate teachers
      await tx.teacher.deleteMany({ where: { chatbotId } })
      if (validatedData.teachers.length > 0) {
        await tx.teacher.createMany({
          data: validatedData.teachers.map(teacher => ({ chatbotId, ...teacher }))
        })
      }

      // Delete and recreate TTCs
      await tx.tTC.deleteMany({ where: { chatbotId } })
      if (validatedData.ttcs.length > 0) {
        await tx.tTC.createMany({
          data: validatedData.ttcs.map(ttc => ({ chatbotId, ...ttc }))
        })
      }

      // Delete and recreate Retreats
      await tx.retreat.deleteMany({ where: { chatbotId } })
      if (validatedData.retreats.length > 0) {
        await tx.retreat.createMany({
          data: validatedData.retreats.map(retreat => ({ chatbotId, ...retreat }))
        })
      }

      // Delete and recreate FAQs
      await tx.fAQ.deleteMany({ where: { chatbotId } })
      if (validatedData.faqs.length > 0) {
        await tx.fAQ.createMany({
          data: validatedData.faqs.map(faq => ({ chatbotId, ...faq }))
        })
      }
    })

    // Trigger background job for processing KB chunks
    // For now, we'll call it directly. In production, you'd use QStash
    try {
      const processResponse = await fetch(`${process.env.NEXTAUTH_URL}/api/queues/process-kb`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ chatbotId })
      })
      
      if (!processResponse.ok) {
        console.error('Failed to trigger KB processing job')
      }
    } catch (error) {
      console.error('Error triggering KB processing:', error)
    }

    return NextResponse.json({
      success: true,
      message: "Structured knowledge base updated successfully. Processing in background..."
    })

  } catch (error) {
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors }, 
        { status: 400 }
      )
    }
    
    console.error("Structured KB API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// GET endpoint to fetch current structured KB data
export async function GET(request: NextRequest) {
  try {
    // Verify user authentication
    const { user } = await verifyUser()

    // Get chatbotId from query parameters
    const { searchParams } = new URL(request.url)
    const chatbotId = searchParams.get('chatbotId')

    if (!chatbotId) {
      return NextResponse.json({ error: "chatbotId is required" }, { status: 400 })
    }

    // Fetch chatbot and verify ownership
    const chatbot = await prisma.chatbot.findFirst({
      where: {
        id: chatbotId,
        userId: user.id
      },
      include: {
        structuredKbBrand: true,
        structuredKbContact: true,
        structuredKbTeachers: true,
        structuredKbTtcs: true,
        structuredKbRetreats: true,
        structuredKbPolicies: true,
        structuredKbFaqs: true,
        _count: {
          select: {
            knowledgeChunks: true
          }
        }
      }
    })

    if (!chatbot) {
      return NextResponse.json({ error: "Chatbot not found" }, { status: 404 })
    }

    return NextResponse.json({
      chatbotId: chatbot.id,
      schoolBrand: chatbot.structuredKbBrand,
      schoolContact: chatbot.structuredKbContact,
      teachers: chatbot.structuredKbTeachers,
      ttcs: chatbot.structuredKbTtcs,
      retreats: chatbot.structuredKbRetreats,
      policies: chatbot.structuredKbPolicies,
      faqs: chatbot.structuredKbFaqs,
      knowledgeChunksCount: chatbot._count.knowledgeChunks
    })

  } catch (error) {
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }

    console.error("Structured KB GET API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
