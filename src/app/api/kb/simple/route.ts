import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { verifyUser } from "@/lib/user-auth"
import { prisma } from "@/lib/prisma"

// Request validation schema
const simpleKbSchema = z.object({
  chatbotId: z.string(),
  text: z.string().min(1, "Knowledge base text cannot be empty")
})

export async function POST(request: NextRequest) {
  try {
    // Verify user authentication
    const { user } = await verifyUser()
    
    // Parse and validate request body
    const body = await request.json()
    const { chatbotId, text } = simpleKbSchema.parse(body)

    // Fetch chatbot and verify ownership
    const chatbot = await prisma.chatbot.findFirst({
      where: { 
        id: chatbotId,
        userId: user.id // Ensure user owns this chatbot
      },
      include: {
        user: {
          include: {
            subscription: {
              include: {
                plan: true
              }
            }
          }
        }
      }
    })

    if (!chatbot) {
      return NextResponse.json({ error: "Chatbot not found" }, { status: 404 })
    }

    // Check subscription status
    if (!chatbot.user.subscription || chatbot.user.subscription.status !== 'active') {
      return NextResponse.json({ error: "Active subscription required" }, { status: 403 })
    }

    // Determine character limit
    let characterLimit = 10000 // Default limit
    
    // Use plan default if available
    const planFeatures = chatbot.user.subscription?.plan.features as any
    if (planFeatures?.simpleKbCharacterLimit) {
      characterLimit = planFeatures.simpleKbCharacterLimit
    }

    // Validate text length against character limit
    if (text.length > characterLimit) {
      return NextResponse.json({ 
        error: `Knowledge base text exceeds the character limit for this bot. Maximum allowed: ${characterLimit.toLocaleString()} characters, provided: ${text.length.toLocaleString()} characters.`,
        characterLimit,
        currentLength: text.length
      }, { status: 400 })
    }

    // Update chatbot with simple KB text
    const updatedChatbot = await prisma.chatbot.update({
      where: { id: chatbotId },
      data: { simpleKbText: text },
      select: {
        id: true,
        simpleKbText: true
      }
    })

    return NextResponse.json({
      success: true,
      message: "Knowledge base updated successfully",
      chatbot: updatedChatbot,
      characterLimit,
      currentLength: text.length
    })

  } catch (error) {
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors }, 
        { status: 400 }
      )
    }
    
    console.error("Simple KB API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// GET endpoint to fetch current simple KB text
export async function GET(request: NextRequest) {
  try {
    // Verify user authentication
    const { user } = await verifyUser()
    
    // Get chatbotId from query parameters
    const { searchParams } = new URL(request.url)
    const chatbotId = searchParams.get('chatbotId')
    
    if (!chatbotId) {
      return NextResponse.json({ error: "chatbotId is required" }, { status: 400 })
    }

    // Fetch chatbot and verify ownership
    const chatbot = await prisma.chatbot.findFirst({
      where: { 
        id: chatbotId,
        userId: user.id
      },
      select: {
        id: true,
        simpleKbText: true,
        user: {
          select: {
            subscription: {
              select: {
                plan: {
                  select: {
                    features: true
                  }
                }
              }
            }
          }
        }
      }
    })

    if (!chatbot) {
      return NextResponse.json({ error: "Chatbot not found" }, { status: 404 })
    }

    // Determine character limit
    let characterLimit = 10000 // Default limit
    
    const planFeatures = chatbot.user.subscription?.plan.features as any
    if (planFeatures?.simpleKbCharacterLimit) {
      characterLimit = planFeatures.simpleKbCharacterLimit
    }

    return NextResponse.json({
      chatbotId: chatbot.id,
      text: chatbot.simpleKbText || '',
      characterLimit,
      currentLength: (chatbot.simpleKbText || '').length
    })

  } catch (error) {
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    
    console.error("Simple KB GET API error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
