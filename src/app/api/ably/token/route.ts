import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import Ably from "ably"
import { getEnv } from "@/lib/env-validation"

export async function POST(request: NextRequest) {
  try {
    // Get the user's session to identify them
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get environment variables
    const env = getEnv()
    
    // Check if Ably API key is configured
    if (!env.ABLY_API_KEY) {
      console.error('ABLY_API_KEY is not configured')
      return NextResponse.json({ error: "Real-time service not configured" }, { status: 500 })
    }

    // Instantiate the Ably REST client using the master API key
    const ably = new Ably.Rest(env.ABLY_API_KEY)
    
    // Create a token request for the authenticated user
    // This token grants them permission to subscribe to specific channels
    const tokenRequest = await ably.auth.createTokenRequest({
      clientId: session.user.id,
      capability: {
        // Allow subscribing to channels for their chatbots
        [`chatbot-${session.user.id}:*`]: ['subscribe', 'presence'],
        // Allow subscribing to live monitor channels for their chatbots
        [`live-monitor:${session.user.id}:*`]: ['subscribe', 'presence'],
        // Allow publishing to chat channels (for human takeover)
        [`chat:*`]: ['publish', 'subscribe'],
        // Allow presence on live monitor channels
        [`presence:*`]: ['presence']
      },
      // Token expires in 1 hour
      ttl: 60 * 60 * 1000
    })

    return NextResponse.json({
      tokenRequest,
      success: true
    })

  } catch (error) {
    console.error('Ably token generation error:', error)
    return NextResponse.json(
      { error: "Failed to generate real-time token" },
      { status: 500 }
    )
  }
}
