import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { GoogleGenerativeAI } from "@google/generative-ai"
import { v4 as uuidv4 } from 'uuid'
import { getEffectiveSystemPrompt } from "@/lib/system-prompt"
import { prisma } from "@/lib/prisma"
import { getEnv } from "@/lib/env-validation"
import { toolSchemas, toolExecutor, type ToolName } from "@/lib/llm-tools"
import { applyRateLimit, handleRouteError, RateLimits } from "@/lib/enhanced-auth"

// LLM providers will be dynamically configured per chatbot

// Request validation schema
const chatRequestSchema = z.object({
  botId: z.string(),
  visitorId: z.string(),
  message: z.string().min(1).max(1000)
})

export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting for public chat endpoint
    await applyRateLimit(request, RateLimits.PUBLIC)
    
    // Parse and validate request body
    const body = await request.json()
    const { botId, visitorId, message } = chatRequestSchema.parse(body)

    // Fetch chatbot with all necessary relations
    const chatbot = await prisma.chatbot.findUnique({
      where: { id: botId },
      include: {
        user: {
          include: {
            subscription: {
              include: {
                plan: true
              }
            }
          }
        },
        personas: {
          where: { isActive: true },
          take: 1
        }
      }
    })

    if (!chatbot) {
      return NextResponse.json({ error: "Chatbot not found" }, { status: 404 })
    }

    // Check subscription status
    if (!chatbot.user.subscription || chatbot.user.subscription.status !== 'active') {
      return NextResponse.json({ error: "Subscription inactive" }, { status: 403 })
    }

    // Check token limits
    const subscription = chatbot.user.subscription
    const features = subscription.plan.features as any
    const tokenLimit = features?.tokenLimit || 10000
    if (subscription.tokensUsedThisPeriod >= tokenLimit) {
      return NextResponse.json({ error: "Token limit exceeded" }, { status: 429 })
    }

    // Get or create visitor
    let visitor = await prisma.visitor.findUnique({
      where: { id: visitorId }
    })

    if (!visitor) {
      visitor = await prisma.visitor.create({
        data: { id: visitorId }
      })
    }

    // Get or create chat session
    let chatSession = await prisma.chatSession.findFirst({
      where: {
        chatbotId: botId,
        visitorId: visitorId
      },
      include: {
        messages: {
          orderBy: { createdAt: 'desc' },
          take: 10 // Last 10 messages for context
        }
      }
    })

    if (!chatSession) {
      chatSession = await prisma.chatSession.create({
        data: {
          chatbotId: botId,
          visitorId: visitorId,
          ablyChannel: `chat-${uuidv4()}`
        },
        include: {
          messages: true
        }
      })
    }

    // Check if human has taken over the conversation
    if (chatSession.controller === 'USER') {
      return NextResponse.json({
        error: "This conversation is currently being handled by a human agent. Please wait for their response."
      }, { status: 423 }) // 423 Locked
    }

    // Save visitor message
    await prisma.message.create({
      data: {
        chatSessionId: chatSession.id,
        senderType: 'VISITOR',
        content: message
      }
    })

    // Determine effective KB type
    const planFeatures = chatbot.user.subscription.plan.features as any
    const effectiveKbType = chatbot.kbTypeOverride || planFeatures?.kbType || 'simple'

    // Retrieve context based on KB strategy
    let context = ''
    
    if (effectiveKbType === 'structured') {
      // Use Full-Text Search for structured KB
      const searchQuery = message.trim().split(/\s+/).join(' & ')
      
      try {
        const results = await prisma.$queryRaw<Array<{ content: string, rank: number }>>`
          SELECT content, ts_rank(content_tsvector, to_tsquery('english', ${searchQuery})) as rank
          FROM "KnowledgeBaseChunk"
          WHERE "chatbotId" = ${botId}
          AND to_tsquery('english', ${searchQuery}) @@ content_tsvector
          ORDER BY rank DESC
          LIMIT 5;
        `
        context = results.map(r => r.content).join('\n---\n')
      } catch (error) {
        console.error('FTS query error:', error)
        // Fallback to simple text search if FTS fails
        context = chatbot.simpleKbText || ''
      }
    } else {
      // Use simple KB text directly
      context = chatbot.simpleKbText || ''
    }

    // Build conversation history
    const conversationHistory = chatSession.messages
      .reverse()
      .map(msg => `${msg.senderType === 'VISITOR' ? 'User' : 'Assistant'}: ${msg.content}`)
      .join('\n')

    // Get active persona
    const persona = chatbot.personas[0]
    const personaText = persona ? persona.personaText : 'You are a helpful yoga assistant.'

    // Get the effective system prompt (either custom or global default)
    const systemPrompt = await getEffectiveSystemPrompt(botId)

    const fullPrompt = `${systemPrompt}

PERSONA:
${personaText}

KNOWLEDGE BASE:
${context}

CONVERSATION HISTORY:
${conversationHistory}

Current User Message: ${message}

Please respond helpfully based on the knowledge base and conversation context. Keep responses concise and friendly.`

    // Get LLM configuration from chatbot or system config
    const env = getEnv()
    let llmApiKey = env.GEMINI_API_KEY
    let llmProvider = chatbot.llmProvider || 'gemini'
    let llmModel = chatbot.llmModel || 'gemini-1.5-flash'

    // If chatbot has BYOK (Bring Your Own Key), use it
    if (chatbot.encryptedLlmApiKey) {
      // TODO: Decrypt the API key here when encryption is implemented
      // llmApiKey = decrypt(chatbot.encryptedLlmApiKey)
    }

    // For now, only support Gemini. In the future, add support for other providers
    if (llmProvider !== 'gemini') {
      throw new Error(`Unsupported LLM provider: ${llmProvider}`)
    }

    // Validate API key is available
    if (!llmApiKey) {
      throw new Error('GEMINI_API_KEY is not configured')
    }

    // Call LLM API with tool support
    const llmClient = new GoogleGenerativeAI(llmApiKey)
    const model = llmClient.getGenerativeModel({
      model: llmModel,
      tools: [{
        functionDeclarations: toolSchemas as any
      }]
    })

    let result = await model.generateContent(fullPrompt)
    let response = result.response
    let responseText = ''
    let totalTokens = 0

    // Check if the LLM wants to use a tool
    const functionCalls = response.functionCalls()

    if (functionCalls && functionCalls.length > 0) {
      // LLM wants to use a tool - execute it and make a second call
      const toolResults = []

      for (const functionCall of functionCalls) {
        const toolName = functionCall.name as ToolName
        const toolArgs = functionCall.args as any || {}

        console.log(`Executing tool: ${toolName} with args:`, toolArgs)

        if (toolExecutor[toolName]) {
          try {
            // Execute the tool function with proper argument handling
            let toolResult;
            if (toolName === 'getTeacherBio') {
              toolResult = await toolExecutor[toolName](botId, toolArgs.teacherName || '')
            } else if (toolName === 'getFAQs') {
              toolResult = await toolExecutor[toolName](botId, toolArgs.searchTerm)
            } else {
              // For tools with no parameters
              toolResult = await toolExecutor[toolName](botId)
            }

            toolResults.push({
              functionResponse: {
                name: toolName,
                response: toolResult
              }
            })
          } catch (error) {
            console.error(`Error executing tool ${toolName}:`, error)
            toolResults.push({
              functionResponse: {
                name: toolName,
                response: { error: `Failed to execute ${toolName}` }
              }
            })
          }
        } else {
          toolResults.push({
            functionResponse: {
              name: toolName,
              response: { error: `Unknown tool: ${toolName}` }
            }
          })
        }
      }

      // Make a second call to the LLM with the tool results
      const secondResult = await model.generateContent([
        { text: fullPrompt },
        { functionCall: functionCalls[0] },
        ...toolResults
      ])

      response = secondResult.response
      responseText = response.text()

      // Estimate tokens for both calls
      totalTokens = Math.ceil((fullPrompt.length * 2 + responseText.length) / 4)
    } else {
      // Normal text response
      responseText = response.text()
      totalTokens = Math.ceil((fullPrompt.length + responseText.length) / 4)
    }

    // Save bot response
    await prisma.message.create({
      data: {
        chatSessionId: chatSession.id,
        senderType: 'LLM',
        content: responseText,
        systemData: functionCalls ? { toolCalls: functionCalls.map(fc => ({ name: fc.name, args: fc.args })) } : undefined
      }
    })

    // Update token usage
    await prisma.subscription.update({
      where: { id: subscription.id },
      data: {
        tokensUsedThisPeriod: {
          increment: totalTokens
        }
      }
    })

    // Update session token count
    await prisma.chatSession.update({
      where: { id: chatSession.id },
      data: {
        tokenCount: {
          increment: totalTokens
        }
      }
    })

    return NextResponse.json({
      response: responseText,
      tokensUsed: totalTokens,
      toolsUsed: functionCalls ? functionCalls.map(fc => fc.name) : []
    })

  } catch (error) {
    return handleRouteError(error)
  }
}
