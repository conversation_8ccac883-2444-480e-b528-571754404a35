import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { z } from "zod"
import { prisma } from "@/lib/prisma"
import { getEnv } from "@/lib/env-validation"
import Ably from "ably"

// Request validation schema
const takeoverRequestSchema = z.object({
  chatSessionId: z.string()
})

export async function POST(request: NextRequest) {
  try {
    // Get the user's session
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Parse and validate request body
    const body = await request.json()
    const { chatSessionId } = takeoverRequestSchema.parse(body)

    // Fetch the chat session and verify ownership
    const chatSession = await prisma.chatSession.findUnique({
      where: { id: chatSessionId },
      include: {
        chatbot: {
          include: {
            user: true
          }
        },
        visitor: true
      }
    })

    if (!chatSession) {
      return NextResponse.json({ error: "Chat session not found" }, { status: 404 })
    }

    // Verify that the user owns this chatbot
    if (chatSession.chatbot.userId !== session.user.id) {
      return NextResponse.json({ error: "Unauthorized access to chat session" }, { status: 403 })
    }

    // Update the chat session controller to USER
    await prisma.chatSession.update({
      where: { id: chatSessionId },
      data: {
        controller: 'USER'
      }
    })

    // Send notification via Ably (if configured)
    const env = getEnv()
    if (env.ABLY_API_KEY) {
      try {
        const ably = new Ably.Rest(env.ABLY_API_KEY)
        const channel = ably.channels.get(chatSession.ablyChannel)
        
        // Notify the widget that a human has taken over
        await channel.publish('takeover', {
          message: 'A team member is joining the conversation...',
          controller: 'USER',
          timestamp: new Date().toISOString()
        })
      } catch (ablyError) {
        console.error('Failed to send Ably notification:', ablyError)
        // Don't fail the request if Ably fails
      }
    }

    return NextResponse.json({
      success: true,
      message: "Chat session taken over successfully",
      chatSession: {
        id: chatSession.id,
        controller: 'USER',
        visitorName: chatSession.visitor.name || 'Anonymous Visitor'
      }
    })

  } catch (error) {
    console.error('Takeover API error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
