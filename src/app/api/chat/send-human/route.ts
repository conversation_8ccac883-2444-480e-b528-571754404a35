import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { z } from "zod"
import { prisma } from "@/lib/prisma"
import { getEnv } from "@/lib/env-validation"
import Ably from "ably"

// Request validation schema
const humanMessageSchema = z.object({
  chatSessionId: z.string(),
  message: z.string().min(1).max(2000)
})

export async function POST(request: NextRequest) {
  try {
    // Get the user's session
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Parse and validate request body
    const body = await request.json()
    const { chatSessionId, message } = humanMessageSchema.parse(body)

    // Fetch the chat session and verify ownership
    const chatSession = await prisma.chatSession.findUnique({
      where: { id: chatSessionId },
      include: {
        chatbot: {
          include: {
            user: true
          }
        },
        visitor: true
      }
    })

    if (!chatSession) {
      return NextResponse.json({ error: "Chat session not found" }, { status: 404 })
    }

    // Verify that the user owns this chatbot
    if (chatSession.chatbot.userId !== session.user.id) {
      return NextResponse.json({ error: "Unauthorized access to chat session" }, { status: 403 })
    }

    // Verify that the session is under human control
    if (chatSession.controller !== 'USER') {
      return NextResponse.json({ 
        error: "Chat session is not under human control. Take over the session first." 
      }, { status: 400 })
    }

    // Save the human message to the database
    const savedMessage = await prisma.message.create({
      data: {
        chatSessionId: chatSessionId,
        senderType: 'USER',
        content: message,
        systemData: {
          humanAgent: {
            userId: session.user.id,
            userName: session.user.name || 'Human Agent'
          }
        }
      }
    })

    // Send message via Ably (if configured)
    const env = getEnv()
    if (env.ABLY_API_KEY) {
      try {
        const ably = new Ably.Rest(env.ABLY_API_KEY)
        const channel = ably.channels.get(chatSession.ablyChannel)
        
        // Publish the message to the chat channel
        await channel.publish('message', {
          id: savedMessage.id,
          senderType: 'USER',
          content: message,
          timestamp: savedMessage.createdAt.toISOString(),
          sender: {
            name: session.user.name || 'Team Member',
            type: 'human'
          }
        })
      } catch (ablyError) {
        console.error('Failed to send Ably message:', ablyError)
        // Don't fail the request if Ably fails
      }
    }

    return NextResponse.json({
      success: true,
      message: {
        id: savedMessage.id,
        content: message,
        senderType: 'USER',
        createdAt: savedMessage.createdAt,
        sender: {
          name: session.user.name || 'Team Member',
          type: 'human'
        }
      }
    })

  } catch (error) {
    console.error('Send human message API error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
