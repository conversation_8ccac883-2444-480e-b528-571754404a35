import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    // Get the user's session
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get active chat sessions for the user's chatbots
    // Consider a session "active" if it has messages in the last 30 minutes
    const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000)

    const activeSessions = await prisma.chatSession.findMany({
      where: {
        chatbot: {
          userId: session.user.id
        },
        messages: {
          some: {
            createdAt: {
              gte: thirtyMinutesAgo
            }
          }
        }
      },
      include: {
        visitor: true,
        chatbot: {
          select: {
            id: true,
            approvedDomain: true
          }
        },
        messages: {
          orderBy: {
            createdAt: 'desc'
          },
          take: 10 // Last 10 messages
        },
        _count: {
          select: {
            messages: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // Transform the data for the frontend
    const transformedSessions = activeSessions.map(session => ({
      id: session.id,
      controller: session.controller,
      ablyChannel: session.ablyChannel,
      createdAt: session.createdAt,
      messageCount: session._count.messages,
      visitor: {
        id: session.visitor.id,
        name: session.visitor.name,
        email: session.visitor.email,
        profileData: session.visitor.profileData
      },
      chatbot: {
        id: session.chatbot.id,
        domain: session.chatbot.approvedDomain
      },
      messages: session.messages.map(msg => ({
        id: msg.id,
        senderType: msg.senderType,
        content: msg.content,
        createdAt: msg.createdAt,
        systemData: msg.systemData
      })),
      lastActivity: session.messages[0]?.createdAt || session.createdAt
    }))

    return NextResponse.json({
      success: true,
      sessions: transformedSessions,
      count: transformedSessions.length
    })

  } catch (error) {
    console.error('Get chat sessions API error:', error)
    
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
