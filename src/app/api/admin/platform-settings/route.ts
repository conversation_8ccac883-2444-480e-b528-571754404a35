import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { verifyAdmin } from "@/lib/admin-auth"

const platformSettingsSchema = z.object({
  defaultLlmModel: z.string().min(1),
  defaultTokenLimit: z.number().min(1000),
  maxChatbotsPerUser: z.number().min(1).max(10),
  enableSignups: z.boolean(),
  enableEmailNotifications: z.boolean(),
  systemMaintenanceMode: z.boolean(),
  defaultWelcomeMessage: z.string().min(1),
  supportEmail: z.string().email(),
  maxFileUploadSize: z.number().min(1).max(100),
  sessionTimeout: z.number().min(5).max(1440)
})

// Default platform settings
const DEFAULT_SETTINGS = {
  defaultLlmModel: "gemini-1.5-flash",
  defaultTokenLimit: 25000,
  maxChatbotsPerUser: 3,
  enableSignups: true,
  enableEmailNotifications: true,
  systemMaintenanceMode: false,
  defaultWelcomeMessage: "Hello! How can I help you today?",
  supportEmail: "<EMAIL>",
  maxFileUploadSize: 10,
  sessionTimeout: 60
}

// In a real application, these would be stored in a database or configuration service
// For now, we'll use environment variables or return defaults
function getStoredSettings() {
  return {
    defaultLlmModel: process.env.DEFAULT_LLM_MODEL || DEFAULT_SETTINGS.defaultLlmModel,
    defaultTokenLimit: parseInt(process.env.DEFAULT_TOKEN_LIMIT || DEFAULT_SETTINGS.defaultTokenLimit.toString()),
    maxChatbotsPerUser: parseInt(process.env.MAX_CHATBOTS_PER_USER || DEFAULT_SETTINGS.maxChatbotsPerUser.toString()),
    enableSignups: process.env.ENABLE_SIGNUPS !== 'false',
    enableEmailNotifications: process.env.ENABLE_EMAIL_NOTIFICATIONS !== 'false',
    systemMaintenanceMode: process.env.SYSTEM_MAINTENANCE_MODE === 'true',
    defaultWelcomeMessage: process.env.DEFAULT_WELCOME_MESSAGE || DEFAULT_SETTINGS.defaultWelcomeMessage,
    supportEmail: process.env.SUPPORT_EMAIL || DEFAULT_SETTINGS.supportEmail,
    maxFileUploadSize: parseInt(process.env.MAX_FILE_UPLOAD_SIZE || DEFAULT_SETTINGS.maxFileUploadSize.toString()),
    sessionTimeout: parseInt(process.env.SESSION_TIMEOUT || DEFAULT_SETTINGS.sessionTimeout.toString())
  }
}

// GET /api/admin/platform-settings - Get platform settings
export async function GET() {
  try {
    await verifyAdmin()

    const settings = getStoredSettings()
    
    return NextResponse.json(settings)

  } catch (error) {
    console.error("Error fetching platform settings:", error)

    if (error instanceof Error && error.message === "Not authorized") {
      return NextResponse.json({ error: "Not authorized" }, { status: 403 })
    }

    return NextResponse.json({ error: "Failed to fetch platform settings" }, { status: 500 })
  }
}

// PUT /api/admin/platform-settings - Update platform settings
export async function PUT(request: NextRequest) {
  try {
    await verifyAdmin()

    const body = await request.json()
    const validatedData = platformSettingsSchema.parse(body)

    // In a real application, you would save these to a database or configuration service
    // For now, we'll just validate and return success
    // You could also update environment variables or a configuration file here

    console.log("Platform settings updated:", validatedData)

    // Here you would typically:
    // 1. Save to database/configuration service
    // 2. Update environment variables
    // 3. Notify other services of configuration changes
    // 4. Restart services if needed

    return NextResponse.json({ 
      success: true, 
      message: "Platform settings updated successfully",
      settings: validatedData
    })

  } catch (error) {
    console.error("Error updating platform settings:", error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }

    if (error instanceof Error && error.message === "Not authorized") {
      return NextResponse.json({ error: "Not authorized" }, { status: 403 })
    }

    return NextResponse.json({ error: "Failed to update platform settings" }, { status: 500 })
  }
}
