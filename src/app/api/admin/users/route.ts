import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { verifyAdmin } from "@/lib/admin-auth"
import bcrypt from "bcryptjs"
import { prisma } from "@/lib/prisma"

// Schema for user creation with subscription
const createUserSchema = z.object({
  email: z.string().email("Invalid email address"),
  name: z.string().min(1, "Name is required"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  planId: z.string().min(1, "Plan selection is required"),
  subscriptionData: z.object({
    status: z.string().default("active"),
    currentPeriodEnd: z.string().transform((str) => new Date(str)),
    razorpaySubscriptionId: z.string().optional(),
  }),
})

// GET /api/admin/users - List all users with their subscriptions
export async function GET() {
  try {
    await verifyAdmin()

    const users = await prisma.user.findMany({
      orderBy: { createdAt: 'desc' },
      include: {
        subscription: {
          include: {
            plan: true
          }
        },
        chatbots: {
          select: {
            id: true,
            approvedDomain: true,
          }
        },
        _count: {
          select: { chatbots: true }
        }
      }
    })

    return NextResponse.json(users)
  } catch (error) {
    if (error instanceof Error && error.message === "Not authorized") {
      return NextResponse.json({ error: "Not authorized" }, { status: 403 })
    }
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    
    console.error("Error fetching users:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// POST /api/admin/users - Create a new user with subscription
export async function POST(request: NextRequest) {
  try {
    await verifyAdmin()

    const body = await request.json()
    const validatedData = createUserSchema.parse(body)

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: validatedData.email }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: "A user with this email already exists" }, 
        { status: 400 }
      )
    }

    // Check if plan exists
    const plan = await prisma.plan.findUnique({
      where: { id: validatedData.planId }
    })

    if (!plan) {
      return NextResponse.json(
        { error: "Selected plan not found" }, 
        { status: 400 }
      )
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(validatedData.password, 12)

    // Create user with subscription in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create user
      const user = await tx.user.create({
        data: {
          email: validatedData.email,
          name: validatedData.name,
          password: hashedPassword,
          role: 'USER',
        }
      })

      // Create subscription
      const subscription = await tx.subscription.create({
        data: {
          userId: user.id,
          planId: validatedData.planId,
          status: validatedData.subscriptionData.status,
          currentPeriodEnd: validatedData.subscriptionData.currentPeriodEnd,
          razorpaySubscriptionId: validatedData.subscriptionData.razorpaySubscriptionId,
          tokensUsedThisPeriod: 0,
          sessionsThisPeriod: 0,
        }
      })

      return { user, subscription }
    })

    // Return user with subscription details
    const userWithSubscription = await prisma.user.findUnique({
      where: { id: result.user.id },
      include: {
        subscription: {
          include: {
            plan: true
          }
        }
      }
    })

    return NextResponse.json(userWithSubscription, { status: 201 })
  } catch (error) {
    if (error instanceof Error && error.message === "Not authorized") {
      return NextResponse.json({ error: "Not authorized" }, { status: 403 })
    }
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors }, 
        { status: 400 }
      )
    }
    
    console.error("Error creating user:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
