import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { verifyAdmin } from "@/lib/admin-auth"
import { prisma } from "@/lib/prisma"

// Schema for user update
const userUpdateSchema = z.object({
  email: z.string().email().optional(),
  name: z.string().optional(),
  planId: z.string().optional(),
  subscriptionData: z.object({
    status: z.string(),
    currentPeriodEnd: z.string(),
  }).optional().nullable(),
  // System prompt settings
  canCustomizeSystemPrompt: z.boolean().optional(),
  useCustomSystemPrompt: z.boolean().optional(),
  customSystemPrompt: z.string().nullable().optional(),
  simpleKbCharacterLimit: z.number().int().min(1000).nullable().optional(),
})

// PUT /api/admin/users/[id] - Update user
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await verifyAdmin()

    const body = await request.json()
    const validatedData = userUpdateSchema.parse(body)
    const { id: userId } = await params

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: userId },
      include: { subscription: true }
    })

    if (!existingUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Update user basic info
    const updateData: any = {}
    if (validatedData.email) updateData.email = validatedData.email
    if (validatedData.name) updateData.name = validatedData.name

    // System prompt settings
    if (validatedData.canCustomizeSystemPrompt !== undefined) {
      updateData.canCustomizeSystemPrompt = validatedData.canCustomizeSystemPrompt
    }
    if (validatedData.useCustomSystemPrompt !== undefined) {
      updateData.useCustomSystemPrompt = validatedData.useCustomSystemPrompt
    }
    if (validatedData.customSystemPrompt !== undefined) {
      updateData.customSystemPrompt = validatedData.customSystemPrompt
    }
    if (validatedData.simpleKbCharacterLimit !== undefined) {
      updateData.simpleKbCharacterLimit = validatedData.simpleKbCharacterLimit
    }

    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: updateData,
    })

    // Handle subscription update
    if (validatedData.planId && validatedData.subscriptionData) {
      // Update or create subscription
      if (existingUser.subscription) {
        await prisma.subscription.update({
          where: { id: existingUser.subscription.id },
          data: {
            planId: validatedData.planId,
            status: validatedData.subscriptionData.status,
            currentPeriodEnd: new Date(validatedData.subscriptionData.currentPeriodEnd),
          }
        })
      } else {
        await prisma.subscription.create({
          data: {
            userId: userId,
            planId: validatedData.planId,
            status: validatedData.subscriptionData.status,
            currentPeriodEnd: new Date(validatedData.subscriptionData.currentPeriodEnd),
            tokensUsedThisPeriod: 0,
          }
        })
      }
    } else if (validatedData.subscriptionData === null && existingUser.subscription) {
      // Remove subscription if planId is empty
      await prisma.subscription.delete({
        where: { id: existingUser.subscription.id }
      })
    }

    return NextResponse.json({ message: "User updated successfully" })
  } catch (error) {
    if (error instanceof Error && error.message === "Not authorized") {
      return NextResponse.json({ error: "Not authorized" }, { status: 403 })
    }
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: "Invalid input data" }, { status: 400 })
    }
    
    console.error("Error updating user:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// DELETE /api/admin/users/[id] - Delete user
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await verifyAdmin()

    const { id: userId } = await params

    // Check if user exists and get chatbot count
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        _count: {
          select: { chatbots: true }
        }
      }
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Prevent deletion if user has chatbots
    if (user._count.chatbots > 0) {
      return NextResponse.json({ 
        error: "Cannot delete user with active chatbots. Please delete chatbots first." 
      }, { status: 400 })
    }

    // Prevent deletion of admin users
    if (user.role === "ADMIN") {
      return NextResponse.json({ 
        error: "Cannot delete admin users" 
      }, { status: 400 })
    }

    // Delete user (subscription will be deleted automatically due to cascade)
    await prisma.user.delete({
      where: { id: userId }
    })

    return NextResponse.json({ message: "User deleted successfully" })
  } catch (error) {
    if (error instanceof Error && error.message === "Not authorized") {
      return NextResponse.json({ error: "Not authorized" }, { status: 403 })
    }
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    
    console.error("Error deleting user:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
