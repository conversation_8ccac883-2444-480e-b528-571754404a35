import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { z } from "zod"

const updateSubscriptionSchema = z.object({
  planId: z.string(),
  tokensUsedThisPeriod: z.number().min(0),
  status: z.enum(["active", "cancelled", "past_due"])
})

interface RouteParams {
  params: Promise<{ id: string }>
}

export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const { id: userId } = await params
    const body = await request.json()
    
    // Validate request body
    const validatedData = updateSubscriptionSchema.parse(body)
    
    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { subscription: true }
    })
    
    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }
    
    // Check if plan exists
    const plan = await prisma.plan.findUnique({
      where: { id: validatedData.planId }
    })
    
    if (!plan) {
      return NextResponse.json(
        { error: "Plan not found" },
        { status: 404 }
      )
    }
    
    // Update or create subscription
    if (user.subscription) {
      // Update existing subscription
      const updatedSubscription = await prisma.subscription.update({
        where: { userId: userId },
        data: {
          planId: validatedData.planId,
          tokensUsedThisPeriod: validatedData.tokensUsedThisPeriod,
          status: validatedData.status
        },
        include: {
          plan: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      })
      
      return NextResponse.json({
        message: "Subscription updated successfully",
        subscription: updatedSubscription
      })
    } else {
      // Create new subscription
      const newSubscription = await prisma.subscription.create({
        data: {
          userId: userId,
          planId: validatedData.planId,
          tokensUsedThisPeriod: validatedData.tokensUsedThisPeriod,
          status: validatedData.status,
          currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
        },
        include: {
          plan: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          }
        }
      })
      
      return NextResponse.json({
        message: "Subscription created successfully",
        subscription: newSubscription
      })
    }
    
  } catch (error) {
    console.error("Error updating user subscription:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
