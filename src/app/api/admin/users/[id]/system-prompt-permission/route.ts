import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { prisma } from "@/lib/prisma"
import { verifyAdmin } from "@/lib/admin-auth"

// Schema for system prompt permission update
const permissionSchema = z.object({
  canCustomizeSystemPrompt: z.boolean()
})

// PUT /api/admin/users/[id]/system-prompt-permission - Update user's system prompt permission
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await verifyAdmin()

    const { id: userId } = await params
    const body = await request.json()
    const validatedData = permissionSchema.parse(body)

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!existingUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Update user's system prompt permission
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        canCustomizeSystemPrompt: validatedData.canCustomizeSystemPrompt
      },
      select: {
        id: true,
        email: true,
        name: true,
        canCustomizeSystemPrompt: true,
        subscription: {
          include: {
            plan: true
          }
        }
      }
    })

    return NextResponse.json(updatedUser)
  } catch (error) {
    if (error instanceof Error && error.message === "Not authorized") {
      return NextResponse.json({ error: "Not authorized" }, { status: 403 })
    }
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors }, 
        { status: 400 }
      )
    }
    
    console.error("Error updating system prompt permission:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
