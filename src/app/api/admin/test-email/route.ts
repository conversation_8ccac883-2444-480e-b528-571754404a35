import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { verifyAdmin } from "@/lib/admin-auth"
import { sendWelcomeEmail, sendSubscriptionWarningEmail, testEmailConfig, parseSmtpConfig } from "@/lib/email-service"

const testEmailSchema = z.object({
  type: z.enum(['welcome', 'warning', 'test']),
  email: z.string().email(),
  smtpConfig: z.object({
    host: z.string(),
    port: z.number(),
    secure: z.boolean(),
    user: z.string(),
    password: z.string()
  }).optional()
})

// POST /api/admin/test-email - Test email functionality
export async function POST(request: NextRequest) {
  try {
    await verifyAdmin()

    const body = await request.json()
    const { type, email, smtpConfig } = testEmailSchema.parse(body)

    let success = false
    let message = ''

    switch (type) {
      case 'welcome':
        success = await sendWelcomeEmail(email, {
          userName: 'Test User',
          planName: 'Pro Plan',
          loginUrl: `${process.env.NEXTAUTH_URL}/login`
        }, smtpConfig)
        message = success ? 'Welcome email sent successfully' : 'Failed to send welcome email'
        break

      case 'warning':
        success = await sendSubscriptionWarningEmail(email, {
          userName: 'Test User',
          tokensUsed: 45000,
          tokenLimit: 50000,
          percentageUsed: 90,
          planName: 'Pro Plan',
          dashboardUrl: `${process.env.NEXTAUTH_URL}/dashboard`
        }, smtpConfig)
        message = success ? 'Warning email sent successfully' : 'Failed to send warning email'
        break

      case 'test':
        if (!smtpConfig) {
          return NextResponse.json(
            { error: "SMTP configuration required for test emails" },
            { status: 400 }
          )
        }
        success = await testEmailConfig(smtpConfig, email)
        message = success ? 'Test email sent successfully' : 'Failed to send test email'
        break

      default:
        return NextResponse.json(
          { error: "Invalid email type" },
          { status: 400 }
        )
    }

    return NextResponse.json({
      success,
      message,
      emailType: type,
      recipient: email
    })

  } catch (error) {
    console.error("Error testing email:", error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      )
    }

    if (error instanceof Error && error.message === "Not authorized") {
      return NextResponse.json({ error: "Not authorized" }, { status: 403 })
    }

    return NextResponse.json({ error: "Failed to test email" }, { status: 500 })
  }
}
