import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { verifyAdmin } from "@/lib/admin-auth"
import { prisma } from "@/lib/prisma"

// Schema for system config validation
const systemConfigSchema = z.object({
  key: z.string().min(1, "Key is required"),
  value: z.string().min(1, "Value is required"),
  description: z.string().optional()
})

// GET /api/admin/system-config - Get all system configurations
export async function GET() {
  try {
    await verifyAdmin()

    const configs = await prisma.systemConfig.findMany({
      orderBy: { key: 'asc' }
    })

    return NextResponse.json(configs)
  } catch (error) {
    if (error instanceof Error && error.message === "Not authorized") {
      return NextResponse.json({ error: "Not authorized" }, { status: 403 })
    }
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    
    console.error("Error fetching system configs:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// POST /api/admin/system-config - Create or update system configuration
export async function POST(request: NextRequest) {
  try {
    await verifyAdmin()

    const body = await request.json()
    const validatedData = systemConfigSchema.parse(body)

    // Upsert the configuration
    const config = await prisma.systemConfig.upsert({
      where: { key: validatedData.key },
      update: {
        value: validatedData.value,
        description: validatedData.description
      },
      create: {
        key: validatedData.key,
        value: validatedData.value,
        description: validatedData.description
      }
    })

    return NextResponse.json(config)
  } catch (error) {
    if (error instanceof Error && error.message === "Not authorized") {
      return NextResponse.json({ error: "Not authorized" }, { status: 403 })
    }
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors }, 
        { status: 400 }
      )
    }
    
    console.error("Error creating/updating system config:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
