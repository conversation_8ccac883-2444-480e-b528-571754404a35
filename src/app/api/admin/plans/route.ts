import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { verifyAdmin } from "@/lib/admin-auth"
import { prisma } from "@/lib/prisma"

// Schema for plan validation
const planSchema = z.object({
  name: z.string().min(1, "Name is required"),
  price: z.number().int().min(0, "Price must be a positive integer"),
  features: z.object({
    chatbotLimit: z.number().int().min(1, "Chatbot limit must be at least 1"),
    tokenLimit: z.number().int().min(1000, "Token limit must be at least 1000"),
    kbType: z.enum(["simple", "structured"], {
      errorMap: () => ({ message: "KB type must be 'simple' or 'structured'" })
    }),
    simpleKbCharacterLimit: z.number().int().min(1000, "Simple KB character limit must be at least 1000"),
    canUseBYOK: z.boolean(),
  }).and(z.record(z.any())), // Allow additional properties
  isActive: z.boolean().optional().default(true),
})

// GET /api/admin/plans - List all plans
export async function GET() {
  try {
    await verifyAdmin()

    const plans = await prisma.plan.findMany({
      orderBy: { name: 'asc' },
      include: {
        _count: {
          select: { subscriptions: true }
        }
      }
    })

    return NextResponse.json(plans)
  } catch (error) {
    if (error instanceof Error && error.message === "Not authorized") {
      return NextResponse.json({ error: "Not authorized" }, { status: 403 })
    }
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    
    console.error("Error fetching plans:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// POST /api/admin/plans - Create a new plan
export async function POST(request: NextRequest) {
  try {
    await verifyAdmin()

    const body = await request.json()
    const validatedData = planSchema.parse(body)

    // Check if plan name already exists
    const existingPlan = await prisma.plan.findUnique({
      where: { name: validatedData.name }
    })

    if (existingPlan) {
      return NextResponse.json(
        { error: "A plan with this name already exists" }, 
        { status: 400 }
      )
    }

    const plan = await prisma.plan.create({
      data: validatedData
    })

    return NextResponse.json(plan, { status: 201 })
  } catch (error) {
    if (error instanceof Error && error.message === "Not authorized") {
      return NextResponse.json({ error: "Not authorized" }, { status: 403 })
    }
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors }, 
        { status: 400 }
      )
    }
    
    console.error("Error creating plan:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
