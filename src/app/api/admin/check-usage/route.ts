import { NextRequest, NextResponse } from "next/server"
import { verifyAdmin } from "@/lib/admin-auth"
import { checkTokenUsageAndSendWarnings, resetTokenUsageForNewPeriods } from "@/lib/token-usage-monitor"

// POST /api/admin/check-usage - Manually trigger token usage check and warnings
export async function POST(request: NextRequest) {
  try {
    await verifyAdmin()

    const body = await request.json()
    const { action } = body

    switch (action) {
      case 'check-warnings':
        await checkTokenUsageAndSendWarnings()
        return NextResponse.json({ 
          success: true, 
          message: "Token usage check completed and warnings sent" 
        })

      case 'reset-periods':
        await resetTokenUsageForNewPeriods()
        return NextResponse.json({ 
          success: true, 
          message: "Token usage reset for expired periods" 
        })

      case 'both':
        await resetTokenUsageForNewPeriods()
        await checkTokenUsageAndSendWarnings()
        return NextResponse.json({ 
          success: true, 
          message: "Both reset and warning check completed" 
        })

      default:
        return NextResponse.json(
          { error: "Invalid action. Use 'check-warnings', 'reset-periods', or 'both'" },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error("Error in usage check:", error)

    if (error instanceof Error && error.message === "Not authorized") {
      return NextResponse.json({ error: "Not authorized" }, { status: 403 })
    }

    return NextResponse.json({ error: "Failed to check usage" }, { status: 500 })
  }
}
