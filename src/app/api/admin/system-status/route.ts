import { NextResponse } from "next/server"
import { verifyAdmin } from "@/lib/admin-auth"
import { prisma } from "@/lib/prisma"
import { getEnv } from "@/lib/env-validation"

// GET /api/admin/system-status - Get overall system health and statistics
export async function GET() {
  try {
    await verifyAdmin()

    const env = getEnv()

    // Check database health
    let databaseStatus: 'healthy' | 'warning' | 'error' = 'healthy'
    try {
      await prisma.$queryRaw`SELECT 1`
    } catch (error) {
      console.error('Database health check failed:', error)
      databaseStatus = 'error'
    }

    // Check email system health
    let emailStatus: 'healthy' | 'warning' | 'error' = 'warning'
    if (env.SMTP_HOST && env.SMTP_USER && env.SMTP_PASSWORD) {
      emailStatus = 'healthy'
    } else {
      emailStatus = 'warning' // Not configured
    }

    // Check billing system health
    let billingStatus: 'healthy' | 'warning' | 'error' = 'warning'
    if (env.RAZORPAY_KEY_ID && env.RAZORPAY_KEY_SECRET) {
      billingStatus = 'healthy'
    } else {
      billingStatus = 'warning' // Not configured
    }

    // Get system statistics
    const [
      totalUsers,
      activeChatbots,
      totalSessions,
      tokensUsedToday
    ] = await Promise.all([
      // Total users
      prisma.user.count(),
      
      // Active chatbots (chatbots with at least one session in the last 30 days)
      prisma.chatbot.count({
        where: {
          chatSessions: {
            some: {
              createdAt: {
                gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
              }
            }
          }
        }
      }),
      
      // Total chat sessions
      prisma.chatSession.count(),
      
      // Tokens used today across all subscriptions
      prisma.subscription.aggregate({
        _sum: {
          tokensUsedThisPeriod: true
        },
        where: {
          status: 'active'
        }
      }).then(result => result._sum.tokensUsedThisPeriod || 0)
    ])

    // Get recent activity (last 24 hours)
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000)
    const [
      newUsersToday,
      newSessionsToday,
      messagestoday
    ] = await Promise.all([
      prisma.user.count({
        where: {
          createdAt: {
            gte: yesterday
          }
        }
      }),
      
      prisma.chatSession.count({
        where: {
          createdAt: {
            gte: yesterday
          }
        }
      }),
      
      prisma.message.count({
        where: {
          createdAt: {
            gte: yesterday
          }
        }
      })
    ])

    // Get subscription statistics
    const subscriptionStats = await prisma.subscription.groupBy({
      by: ['status'],
      _count: {
        status: true
      }
    })

    // Get plan distribution
    const planStats = await prisma.subscription.groupBy({
      by: ['planId'],
      _count: {
        planId: true
      },
      include: {
        plan: {
          select: {
            name: true
          }
        }
      }
    })

    // Get high usage subscriptions (>80% token usage)
    const highUsageSubscriptions = await prisma.subscription.count({
      where: {
        status: 'active',
        AND: [
          {
            tokensUsedThisPeriod: {
              gt: 0
            }
          }
        ]
      },
      // Note: We can't easily do percentage calculation in Prisma
      // This would need to be done with raw SQL or in application logic
    })

    return NextResponse.json({
      // System health
      database: databaseStatus,
      email: emailStatus,
      billing: billingStatus,
      
      // Core statistics
      totalUsers,
      activeChatbots,
      totalSessions,
      tokensUsedToday,
      
      // Recent activity (24h)
      recentActivity: {
        newUsers: newUsersToday,
        newSessions: newSessionsToday,
        messages: messagestoday
      },
      
      // Subscription statistics
      subscriptions: {
        byStatus: subscriptionStats.reduce((acc, stat) => {
          acc[stat.status] = stat._count.status
          return acc
        }, {} as Record<string, number>),
        
        byPlan: planStats.map(stat => ({
          planId: stat.planId,
          count: stat._count.planId
        })),
        
        highUsage: highUsageSubscriptions
      },
      
      // System configuration status
      configuration: {
        geminiConfigured: !!env.GEMINI_API_KEY,
        ablyConfigured: !!env.ABLY_API_KEY,
        encryptionConfigured: !!env.ENCRYPTION_KEY,
        qstashConfigured: !!env.QSTASH_TOKEN,
        smtpConfigured: !!(env.SMTP_HOST && env.SMTP_USER && env.SMTP_PASSWORD),
        razorpayConfigured: !!(env.RAZORPAY_KEY_ID && env.RAZORPAY_KEY_SECRET)
      },
      
      // Timestamp
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error("Error fetching system status:", error)

    if (error instanceof Error && error.message === "Not authorized") {
      return NextResponse.json({ error: "Not authorized" }, { status: 403 })
    }

    return NextResponse.json({ error: "Failed to fetch system status" }, { status: 500 })
  }
}
