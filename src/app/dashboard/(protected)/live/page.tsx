'use client'

import { useState, useEffect } from 'react'
import { Message<PERSON>quare, <PERSON>, Clock, Setting<PERSON>, ArrowRight, <PERSON>r<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Send } from "lucide-react"
import Ably from 'ably'

interface Visitor {
  id: string
  name?: string
  email?: string
  lastSeen: Date
  isActive: boolean
  chatSessionId?: string
}

interface Message {
  id: string
  senderType: 'VISITOR' | 'LLM' | 'USER'
  content: string
  createdAt: Date
}

interface ChatSession {
  id: string
  visitorId: string
  visitor: Visitor
  controller: 'LLM' | 'USER'
  messages: Message[]
  ablyChannel: string
}

export default function LiveChatPage() {
  const [activeSessions, setActiveSessions] = useState<ChatSession[]>([])
  const [selectedSession, setSelectedSession] = useState<ChatSession | null>(null)
  const [messages, setMessages] = useState<Message[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [isConnected, setIsConnected] = useState(false)
  const [ably, setAbly] = useState<Ably.Realtime | null>(null)
  const [isTakenOver, setIsTakenOver] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  // Fetch active chat sessions
  const fetchActiveSessions = async () => {
    try {
      const response = await fetch('/api/chat/sessions')
      if (response.ok) {
        const data = await response.json()
        setActiveSessions(data.sessions || [])
      }
    } catch (error) {
      console.error('Failed to fetch active sessions:', error)
    }
  }

  // Initialize Ably connection and fetch sessions
  useEffect(() => {
    const initializeAbly = async () => {
      try {
        // Fetch initial active sessions
        await fetchActiveSessions()

        // Get token from our API
        const response = await fetch('/api/ably/token', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        })

        if (!response.ok) {
          throw new Error('Failed to get Ably token')
        }

        const { tokenRequest } = await response.json()

        // Initialize Ably client with the token
        const ablyClient = new Ably.Realtime({
          authCallback: (tokenParams, callback) => {
            callback(null, tokenRequest)
          }
        })

        ablyClient.connection.on('connected', () => {
          setIsConnected(true)
          setIsLoading(false)
        })

        ablyClient.connection.on('disconnected', () => {
          setIsConnected(false)
        })

        setAbly(ablyClient)

        // Subscribe to presence events for active visitors
        // This would be implemented when we have actual chat sessions

      } catch (error) {
        console.error('Failed to initialize Ably:', error)
        setIsLoading(false)
      }
    }

    initializeAbly()

    // Refresh sessions every 30 seconds
    const interval = setInterval(fetchActiveSessions, 30000)

    return () => {
      if (ably) {
        ably.close()
      }
      clearInterval(interval)
    }
  }, [])

  const handleTakeOver = async () => {
    if (!selectedSession) return

    try {
      const response = await fetch('/api/chat/takeover', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          chatSessionId: selectedSession.id
        })
      })

      if (response.ok) {
        setIsTakenOver(true)
        // Update the session controller status
        setSelectedSession(prev => prev ? { ...prev, controller: 'USER' } : null)
      }
    } catch (error) {
      console.error('Failed to take over chat:', error)
    }
  }

  const handleSendMessage = async () => {
    if (!newMessage.trim() || !selectedSession) return

    try {
      const response = await fetch('/api/chat/send-human', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          chatSessionId: selectedSession.id,
          message: newMessage
        })
      })

      if (response.ok) {
        // Add message to local state
        const newMsg: Message = {
          id: Date.now().toString(),
          senderType: 'USER',
          content: newMessage,
          createdAt: new Date()
        }
        setMessages(prev => [...prev, newMsg])
        setNewMessage('')
      }
    } catch (error) {
      console.error('Failed to send message:', error)
    }
  }

  const chatStats = [
    { label: "Active Chats", value: activeSessions.length.toString(), color: "text-green-600" },
    { label: "Connected", value: isConnected ? "Yes" : "No", color: isConnected ? "text-green-600" : "text-red-600" },
    { label: "Human Control", value: activeSessions.filter(s => s.controller === 'USER').length.toString(), color: "text-blue-600" },
    { label: "Status", value: isLoading ? "Loading..." : "Ready", color: "text-purple-600" }
  ]

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-slate-600">Connecting to real-time service...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="relative overflow-hidden bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 rounded-2xl p-8 text-white">
        <div className="relative z-10">
          <div className="flex items-center space-x-3 mb-4">
            <MessageSquare className="h-8 w-8 text-green-300" />
            <h1 className="text-4xl font-bold">Live Chat Monitor</h1>
          </div>
          <p className="text-green-100 text-lg">
            Monitor active conversations and take over when needed
          </p>
        </div>
        <div className="absolute top-0 right-0 -mt-4 -mr-4 w-32 h-32 bg-white/10 rounded-full"></div>
      </div>

      {/* Chat Stats */}
      <div>
        <h2 className="text-2xl font-bold text-slate-800 mb-6">Live Overview</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {chatStats.map((stat, index) => (
            <div key={index} className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
              <p className="text-slate-600 text-sm font-medium mb-2">{stat.label}</p>
              <p className={`text-3xl font-bold ${stat.color}`}>{stat.value}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Two-Panel Interface */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="bg-gradient-to-r from-slate-50 to-gray-50 px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-slate-800">Live Chat Interface</h3>
        </div>

        <div className="flex h-96">
          {/* Left Panel - Active Visitors */}
          <div className="w-1/3 border-r border-gray-200 bg-slate-50">
            <div className="p-4 border-b border-gray-200">
              <h4 className="font-semibold text-slate-800 flex items-center">
                <Users className="h-4 w-4 mr-2" />
                Active Conversations ({activeSessions.length})
              </h4>
            </div>
            <div className="overflow-y-auto h-full">
              {activeSessions.length === 0 ? (
                <div className="p-4 text-center text-slate-500">
                  <Users className="h-8 w-8 mx-auto mb-2 text-slate-400" />
                  <p className="text-sm">No active conversations</p>
                  <p className="text-xs text-slate-400 mt-1">Conversations will appear here when visitors start chatting</p>
                </div>
              ) : (
                activeSessions.map((session) => (
                  <div
                    key={session.id}
                    className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-white transition-colors ${
                      selectedSession?.id === session.id ? 'bg-white border-l-4 border-l-green-500' : ''
                    }`}
                    onClick={() => {
                      setSelectedSession(session)
                      setMessages(session.messages)
                      setIsTakenOver(session.controller === 'USER')
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-slate-800">
                          {session.visitor.name || 'Anonymous Visitor'}
                        </p>
                        <p className="text-xs text-slate-500">
                          {session.visitor.email || 'No email provided'}
                        </p>
                        <p className="text-xs text-slate-400 mt-1">
                          {session.messages.length} messages • {session.controller === 'LLM' ? 'AI' : 'Human'} control
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className={`w-2 h-2 rounded-full ${session.controller === 'USER' ? 'bg-blue-500' : 'bg-green-500'}`}></div>
                        <Clock className="h-3 w-3 text-slate-400" />
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Right Panel - Conversation */}
          <div className="flex-1 flex flex-col">
            {selectedSession ? (
              <>
                {/* Chat Header */}
                <div className="p-4 border-b border-gray-200 bg-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <h5 className="font-semibold text-slate-800">
                        {selectedSession.visitor.name || 'Anonymous Visitor'}
                      </h5>
                      <p className="text-xs text-slate-500">
                        Controller: {selectedSession.controller === 'LLM' ? 'AI Bot' : 'Human Agent'}
                      </p>
                    </div>
                    {selectedSession.controller === 'LLM' && (
                      <button
                        onClick={handleTakeOver}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                      >
                        <UserCheck className="h-4 w-4" />
                        <span>Take Over</span>
                      </button>
                    )}
                  </div>
                </div>

                {/* Messages */}
                <div className="flex-1 overflow-y-auto p-4 space-y-4">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.senderType === 'VISITOR' ? 'justify-start' : 'justify-end'}`}
                    >
                      <div
                        className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                          message.senderType === 'VISITOR'
                            ? 'bg-gray-200 text-gray-800'
                            : message.senderType === 'LLM'
                            ? 'bg-blue-500 text-white'
                            : 'bg-green-500 text-white'
                        }`}
                      >
                        <p className="text-sm">{message.content}</p>
                        <p className="text-xs opacity-75 mt-1">
                          {message.senderType === 'VISITOR' ? 'Visitor' :
                           message.senderType === 'LLM' ? 'AI Bot' : 'You'}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Message Input (only if taken over) */}
                {selectedSession.controller === 'USER' && (
                  <div className="p-4 border-t border-gray-200 bg-white">
                    <div className="flex space-x-2">
                      <input
                        type="text"
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        placeholder="Type your message..."
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                        onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                      />
                      <button
                        onClick={handleSendMessage}
                        disabled={!newMessage.trim()}
                        className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      >
                        <Send className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="flex-1 flex items-center justify-center">
                <div className="text-center">
                  <MessageSquare className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-slate-600 mb-2">Select a conversation</h3>
                  <p className="text-slate-500 text-sm">Choose a visitor from the left panel to view their conversation</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Connection Status */}
      <div className={`rounded-2xl p-6 ${isConnected ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
        <div className="flex items-start space-x-4">
          <div className="flex-shrink-0">
            <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
          </div>
          <div>
            <h3 className={`text-lg font-semibold mb-2 ${isConnected ? 'text-green-900' : 'text-red-900'}`}>
              Real-time Connection {isConnected ? 'Active' : 'Disconnected'}
            </h3>
            <p className={`text-sm ${isConnected ? 'text-green-800' : 'text-red-800'}`}>
              {isConnected
                ? 'You are connected to the real-time chat service. New conversations will appear automatically.'
                : 'Connection to real-time service failed. Please refresh the page or check your internet connection.'
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
