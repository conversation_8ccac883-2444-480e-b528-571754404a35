'use client'

import { useState, useEffect } from 'react'
import { BarChart3, TrendingUp, Users, Mail, Download, Search, Filter, Calendar } from "lucide-react"

interface Lead {
  id: string
  name: string
  email: string
  phone: string
  company: string
  source: string
  firstSeenAt: string
  lastSeenAt: string
  totalSessions: number
  totalMessages: number
  status: string
  profileData: any
}

interface LeadStats {
  totalLeads: number
  thisWeek: number
  thisMonth: number
  conversionRate: number
  avgResponseTime: number
}

export default function LeadsPage() {
  const [leads, setLeads] = useState<Lead[]>([])
  const [stats, setStats] = useState<LeadStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [sortBy, setSortBy] = useState('lastSeenAt')

  // Fetch leads and stats on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch leads
        const leadsResponse = await fetch('/api/user/leads')
        if (leadsResponse.ok) {
          const leadsData = await leadsResponse.json()
          setLeads(leadsData)
        }

        // Fetch stats
        const statsResponse = await fetch('/api/user/leads', { method: 'POST' })
        if (statsResponse.ok) {
          const statsData = await statsResponse.json()
          setStats(statsData)
        }
      } catch (error) {
        console.error('Error fetching leads data:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  // Filter and sort leads
  const filteredLeads = leads
    .filter(lead => {
      const matchesSearch = lead.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           lead.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           lead.company.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesStatus = statusFilter === 'all' || lead.status.toLowerCase() === statusFilter.toLowerCase()
      return matchesSearch && matchesStatus
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name)
        case 'email':
          return a.email.localeCompare(b.email)
        case 'lastSeenAt':
          return new Date(b.lastSeenAt).getTime() - new Date(a.lastSeenAt).getTime()
        case 'totalSessions':
          return b.totalSessions - a.totalSessions
        default:
          return 0
      }
    })

  const handleExportCSV = async () => {
    try {
      const response = await fetch('/api/user/leads?format=csv')
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = 'leads.csv'
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      }
    } catch (error) {
      console.error('Error exporting CSV:', error)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active': return 'bg-green-100 text-green-800'
      case 'recent': return 'bg-blue-100 text-blue-800'
      case 'warm': return 'bg-yellow-100 text-yellow-800'
      case 'cold': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="relative overflow-hidden bg-gradient-to-r from-purple-600 via-violet-600 to-indigo-600 rounded-2xl p-8 text-white">
        <div className="relative z-10">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <BarChart3 className="h-8 w-8 text-purple-300" />
                <h1 className="text-4xl font-bold">Analytics & Leads</h1>
              </div>
              <p className="text-purple-100 text-lg">
                View and manage leads generated from your chatbot
              </p>
            </div>
            <button
              onClick={handleExportCSV}
              className="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-lg font-medium transition-all flex items-center space-x-2"
            >
              <Download className="h-4 w-4" />
              <span>Export CSV</span>
            </button>
          </div>
        </div>
        <div className="absolute top-0 right-0 -mt-4 -mr-4 w-32 h-32 bg-white/10 rounded-full"></div>
      </div>

      {/* Lead Stats */}
      <div>
        <h2 className="text-2xl font-bold text-slate-800 mb-6">Lead Overview</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <div className="flex items-center justify-between mb-2">
              <p className="text-slate-600 text-sm font-medium">Total Leads</p>
              <Users className="h-4 w-4 text-blue-500" />
            </div>
            <p className="text-3xl font-bold text-slate-800 mb-1">{stats?.totalLeads || 0}</p>
            <p className="text-sm text-slate-600">All time</p>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <div className="flex items-center justify-between mb-2">
              <p className="text-slate-600 text-sm font-medium">This Week</p>
              <Calendar className="h-4 w-4 text-green-500" />
            </div>
            <p className="text-3xl font-bold text-slate-800 mb-1">{stats?.thisWeek || 0}</p>
            <p className="text-sm text-slate-600">Last 7 days</p>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <div className="flex items-center justify-between mb-2">
              <p className="text-slate-600 text-sm font-medium">This Month</p>
              <TrendingUp className="h-4 w-4 text-purple-500" />
            </div>
            <p className="text-3xl font-bold text-slate-800 mb-1">{stats?.thisMonth || 0}</p>
            <p className="text-sm text-slate-600">Last 30 days</p>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <div className="flex items-center justify-between mb-2">
              <p className="text-slate-600 text-sm font-medium">Conversion Rate</p>
              <BarChart3 className="h-4 w-4 text-orange-500" />
            </div>
            <p className="text-3xl font-bold text-slate-800 mb-1">{stats?.conversionRate || 0}%</p>
            <p className="text-sm text-slate-600">Visitors to leads</p>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 mb-6">
          <h3 className="text-lg font-semibold text-slate-800">Lead Management</h3>
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <input
                type="text"
                placeholder="Search leads..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="recent">Recent</option>
              <option value="warm">Warm</option>
              <option value="cold">Cold</option>
            </select>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="lastSeenAt">Last Seen</option>
              <option value="name">Name</option>
              <option value="email">Email</option>
              <option value="totalSessions">Sessions</option>
            </select>
          </div>
        </div>

        {/* Leads Table */}
        {filteredLeads.length === 0 ? (
          <div className="text-center py-12">
            <Users className="h-12 w-12 text-slate-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-slate-800 mb-2">No leads found</h3>
            <p className="text-slate-600">
              {leads.length === 0
                ? "Start conversations with your chatbot to generate leads."
                : "Try adjusting your search or filter criteria."
              }
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-slate-700">Name</th>
                  <th className="text-left py-3 px-4 font-medium text-slate-700">Email</th>
                  <th className="text-left py-3 px-4 font-medium text-slate-700">Company</th>
                  <th className="text-left py-3 px-4 font-medium text-slate-700">Source</th>
                  <th className="text-left py-3 px-4 font-medium text-slate-700">Status</th>
                  <th className="text-left py-3 px-4 font-medium text-slate-700">Sessions</th>
                  <th className="text-left py-3 px-4 font-medium text-slate-700">Last Seen</th>
                </tr>
              </thead>
              <tbody>
                {filteredLeads.map((lead) => (
                  <tr key={lead.id} className="border-b border-gray-100 hover:bg-slate-50">
                    <td className="py-3 px-4">
                      <div>
                        <p className="font-medium text-slate-800">{lead.name}</p>
                        {lead.phone && (
                          <p className="text-sm text-slate-600">{lead.phone}</p>
                        )}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <a href={`mailto:${lead.email}`} className="text-blue-600 hover:text-blue-700">
                        {lead.email}
                      </a>
                    </td>
                    <td className="py-3 px-4 text-slate-600">
                      {lead.company || '-'}
                    </td>
                    <td className="py-3 px-4 text-slate-600">
                      {lead.source}
                    </td>
                    <td className="py-3 px-4">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(lead.status)}`}>
                        {lead.status}
                      </span>
                    </td>
                    <td className="py-3 px-4 text-slate-600">
                      {lead.totalSessions}
                    </td>
                    <td className="py-3 px-4 text-slate-600">
                      {new Date(lead.lastSeenAt).toLocaleDateString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  )
}
