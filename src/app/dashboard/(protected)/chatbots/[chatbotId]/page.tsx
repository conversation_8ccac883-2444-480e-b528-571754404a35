import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { redirect, notFound } from "next/navigation"
import { prisma } from "@/lib/prisma"
import { <PERSON><PERSON>, ArrowLeft } from "lucide-react"
import Link from "next/link"
import ChatbotSettingsClient from "@/components/ChatbotSettingsClient"

interface ChatbotSettingsPageProps {
  params: Promise<{
    chatbotId: string
  }>
}

export default async function ChatbotSettingsPage({ params }: ChatbotSettingsPageProps) {
  // Get user session
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) {
    redirect('/login')
  }

  const { chatbotId } = await params

  // Fetch the specific chatbot with all related data
  const chatbot = await prisma.chatbot.findFirst({
    where: {
      id: chatbotId,
      userId: session.user.id // Ensure user owns this chatbot
    },
    include: {
      assignedKb: {
        select: {
          id: true,
          name: true,
          kbType: true,
          description: true
        }
      },
      user: {
        select: {
          subscription: {
            include: {
              plan: true
            }
          }
        }
      },
      _count: {
        select: {
          chatSessions: true
        }
      }
    }
  })

  if (!chatbot) {
    notFound()
  }

  // Fetch user's knowledge bases for assignment dropdown
  const knowledgeBases = await prisma.knowledgeBase.findMany({
    where: { userId: session.user.id },
    select: {
      id: true,
      name: true,
      kbType: true,
      description: true,
      assignedChatbot: {
        select: {
          id: true,
          approvedDomain: true
        }
      }
    },
    orderBy: { name: 'asc' }
  })

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 rounded-2xl p-8 text-white">
        <div className="relative z-10">
          <div className="flex items-center space-x-3 mb-4">
            <Link
              href="/dashboard/chatbots"
              className="inline-flex items-center text-blue-200 hover:text-white transition-colors"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Chatbots
            </Link>
          </div>
          <div className="flex items-center space-x-3 mb-4">
            <Bot className="h-8 w-8 text-blue-300" />
            <h1 className="text-4xl font-bold">Chatbot Settings</h1>
          </div>
          <p className="text-blue-100 text-lg">
            Configure your chatbot: {chatbot.approvedDomain}
          </p>
        </div>
        <div className="absolute top-0 right-0 -mt-4 -mr-4 w-32 h-32 bg-white/10 rounded-full"></div>
      </div>

      {/* Chatbot Settings Client Component */}
      <ChatbotSettingsClient 
        chatbot={chatbot} 
        knowledgeBases={knowledgeBases}
      />
    </div>
  )
}
