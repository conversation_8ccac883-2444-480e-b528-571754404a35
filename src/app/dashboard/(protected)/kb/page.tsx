import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { redirect } from "next/navigation"
import { prisma } from "@/lib/prisma"
import { Database, Plus } from "lucide-react"
import KnowledgeBasesClient from "@/components/KnowledgeBasesClient"

export default async function KnowledgeBasePage() {
  // Get user session
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) {
    redirect('/login')
  }

  // Fetch user's knowledge bases and subscription info
  const user = await prisma.user.findUnique({
    where: { id: session.user.id },
    include: {
      subscription: {
        include: {
          plan: true
        }
      },
      knowledgeBases: {
        include: {
          assignedChatbot: {
            select: {
              id: true,
              approvedDomain: true
            }
          },
          _count: {
            select: {
              knowledgeChunks: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }
    }
  })

  if (!user) {
    redirect('/login')
  }

  return (
    <div className="space-y-8">
      {/* <PERSON>er */}
      <div className="relative overflow-hidden bg-gradient-to-r from-green-600 via-teal-600 to-cyan-600 rounded-2xl p-8 text-white">
        <div className="relative z-10">
          <div className="flex items-center space-x-3 mb-4">
            <Database className="h-8 w-8 text-green-300" />
            <h1 className="text-4xl font-bold">Knowledge Bases</h1>
          </div>
          <p className="text-green-100 text-lg">
            Create and manage your knowledge bases independently
          </p>
        </div>
        <div className="absolute top-0 right-0 -mt-4 -mr-4 w-32 h-32 bg-white/10 rounded-full"></div>
      </div>

      {/* Knowledge Bases Client Component */}
      <KnowledgeBasesClient user={user} />
    </div>
  )
}
