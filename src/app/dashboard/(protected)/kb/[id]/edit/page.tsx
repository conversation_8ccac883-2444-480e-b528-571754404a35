import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { redirect, notFound } from "next/navigation"
import { prisma } from "@/lib/prisma"
import { Database, ArrowLeft } from "lucide-react"
import Link from "next/link"
import SimpleKBTextEditor from "@/components/SimpleKBTextEditor"
import StructuredKBDataEditor from "@/components/StructuredKBDataEditor"

interface KBEditPageProps {
  params: Promise<{
    id: string
  }>
}

export default async function KBEditPage({ params }: KBEditPageProps) {
  // Get user session
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) {
    redirect('/login')
  }

  const { id } = await params

  // Fetch the specific knowledge base with all related data
  const knowledgeBase = await prisma.knowledgeBase.findFirst({
    where: { 
      id,
      userId: session.user.id // Ensure user owns this KB
    },
    include: {
      // Simple KB data is already included
      // Structured KB data
      structuredKbBrand: true,
      structuredKbContact: true,
      structuredKbTeachers: true,
      structuredKbTtcs: true,
      structuredKbRetreats: true,
      structuredKbPolicies: true,
      structuredKbFaqs: true,
      _count: {
        select: {
          knowledgeChunks: true
        }
      }
    }
  })

  if (!knowledgeBase) {
    notFound()
  }

  // Get user's plan to check character limits
  const user = await prisma.user.findUnique({
    where: { id: session.user.id },
    include: {
      subscription: {
        include: {
          plan: true
        }
      }
    }
  })

  // Determine character limit for simple KB
  let characterLimit = 10000 // Default limit
  if (user?.subscription?.plan) {
    // Plan-based limit
    const planFeatures = user.subscription.plan.features as any
    characterLimit = planFeatures?.simpleKbCharacterLimit || 10000
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 rounded-2xl p-8 text-white">
        <div className="relative z-10">
          <div className="flex items-center space-x-3 mb-4">
            <Link
              href={`/dashboard/kb/${knowledgeBase.id}`}
              className="inline-flex items-center text-blue-200 hover:text-white transition-colors"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to KB Settings
            </Link>
          </div>
          <div className="flex items-center space-x-3 mb-4">
            <Database className="h-8 w-8 text-blue-300" />
            <h1 className="text-4xl font-bold">
              Edit {knowledgeBase.kbType === 'simple' ? 'Text Content' : 'Structured Data'}
            </h1>
          </div>
          <p className="text-blue-100 text-lg">
            {knowledgeBase.name} • {knowledgeBase.kbType} Knowledge Base
          </p>
        </div>
        <div className="absolute top-0 right-0 -mt-4 -mr-4 w-32 h-32 bg-white/10 rounded-full"></div>
      </div>

      {/* Editor Component */}
      {knowledgeBase.kbType === 'simple' ? (
        <SimpleKBTextEditor
          knowledgeBase={knowledgeBase}
          characterLimit={characterLimit}
        />
      ) : (
        <StructuredKBDataEditor
          knowledgeBase={knowledgeBase}
        />
      )}
    </div>
  )
}
