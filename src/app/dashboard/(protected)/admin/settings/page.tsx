'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Settings, Save, AlertCircle, CheckCircle, Mail, Shield, Zap, Globe } from "lucide-react"

interface PlatformSettings {
  defaultLlmModel: string
  defaultTokenLimit: number
  maxChatbotsPerUser: number
  enableSignups: boolean
  enableEmailNotifications: boolean
  systemMaintenanceMode: boolean
  defaultWelcomeMessage: string
  supportEmail: string
  maxFileUploadSize: number
  sessionTimeout: number
}

export default function AdminSettingsPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const [settings, setSettings] = useState<PlatformSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)

  useEffect(() => {
    if (session?.user?.role !== 'ADMIN') {
      router.push('/dashboard')
      return
    }

    fetchSettings()
  }, [session, router])

  const fetchSettings = async () => {
    try {
      const response = await fetch('/api/admin/platform-settings')
      if (response.ok) {
        const data = await response.json()
        setSettings(data)
      } else {
        console.error('Failed to fetch settings')
      }
    } catch (error) {
      console.error('Error fetching settings:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async () => {
    if (!settings) return

    setSaving(true)
    setMessage(null)

    try {
      const response = await fetch('/api/admin/platform-settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      })

      if (response.ok) {
        setMessage({ type: 'success', text: 'Platform settings saved successfully!' })
      } else {
        const error = await response.json()
        setMessage({ type: 'error', text: error.error || 'Failed to save settings' })
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'An error occurred while saving settings' })
    } finally {
      setSaving(false)
    }
  }

  const updateSetting = (key: keyof PlatformSettings, value: any) => {
    if (!settings) return
    setSettings({
      ...settings,
      [key]: value
    })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!settings) {
    return (
      <div className="text-center py-8">
        <p className="text-slate-600">Failed to load platform settings. Please try again.</p>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="relative overflow-hidden bg-gradient-to-r from-gray-600 via-slate-600 to-zinc-600 rounded-2xl p-8 text-white">
        <div className="relative z-10">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <Settings className="h-8 w-8 text-gray-300" />
                <h1 className="text-4xl font-bold">Platform Settings</h1>
              </div>
              <p className="text-gray-100 text-lg">
                Configure system-wide settings and platform defaults
              </p>
            </div>
            <button
              onClick={handleSave}
              disabled={saving}
              className="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-lg font-medium transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Saving...</span>
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  <span>Save Settings</span>
                </>
              )}
            </button>
          </div>
        </div>
        <div className="absolute top-0 right-0 -mt-4 -mr-4 w-32 h-32 bg-white/10 rounded-full"></div>
      </div>

      {/* Message */}
      {message && (
        <div className={`rounded-lg p-4 flex items-center space-x-3 ${
          message.type === 'success'
            ? 'bg-green-50 border border-green-200 text-green-800'
            : 'bg-red-50 border border-red-200 text-red-800'
        }`}>
          {message.type === 'success' ? (
            <CheckCircle className="h-5 w-5" />
          ) : (
            <AlertCircle className="h-5 w-5" />
          )}
          <span>{message.text}</span>
        </div>
      )}

      {/* Settings Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* AI & Performance Settings */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
          <div className="flex items-center space-x-3 mb-6">
            <Zap className="h-6 w-6 text-yellow-600" />
            <h3 className="text-lg font-semibold text-slate-800">AI & Performance</h3>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Default LLM Model
              </label>
              <select
                value={settings.defaultLlmModel}
                onChange={(e) => updateSetting('defaultLlmModel', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="gemini-1.5-flash">Gemini 1.5 Flash (Fast)</option>
                <option value="gemini-1.5-pro">Gemini 1.5 Pro (Accurate)</option>
                <option value="gpt-4o-mini">GPT-4o Mini</option>
                <option value="gpt-4o">GPT-4o</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Default Token Limit (per month)
              </label>
              <input
                type="number"
                value={settings.defaultTokenLimit}
                onChange={(e) => updateSetting('defaultTokenLimit', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                min="1000"
                step="1000"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Max Chatbots per User
              </label>
              <input
                type="number"
                value={settings.maxChatbotsPerUser}
                onChange={(e) => updateSetting('maxChatbotsPerUser', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                min="1"
                max="10"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Session Timeout (minutes)
              </label>
              <input
                type="number"
                value={settings.sessionTimeout}
                onChange={(e) => updateSetting('sessionTimeout', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                min="5"
                max="1440"
              />
            </div>
          </div>
        </div>

        {/* Platform Controls */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
          <div className="flex items-center space-x-3 mb-6">
            <Globe className="h-6 w-6 text-blue-600" />
            <h3 className="text-lg font-semibold text-slate-800">Platform Controls</h3>
          </div>

          <div className="space-y-4">
            <div>
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={settings.enableSignups}
                  onChange={(e) => updateSetting('enableSignups', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm font-medium text-slate-700">Enable New User Signups</span>
              </label>
              <p className="text-xs text-slate-500 mt-1">Allow new users to register via /signup page</p>
            </div>

            <div>
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={settings.enableEmailNotifications}
                  onChange={(e) => updateSetting('enableEmailNotifications', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm font-medium text-slate-700">Enable Email Notifications</span>
              </label>
              <p className="text-xs text-slate-500 mt-1">Send automated emails for welcome, warnings, etc.</p>
            </div>

            <div>
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={settings.systemMaintenanceMode}
                  onChange={(e) => updateSetting('systemMaintenanceMode', e.target.checked)}
                  className="rounded border-gray-300 text-red-600 focus:ring-red-500"
                />
                <span className="text-sm font-medium text-slate-700">Maintenance Mode</span>
              </label>
              <p className="text-xs text-slate-500 mt-1">Disable platform for maintenance (admin access only)</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Max File Upload Size (MB)
              </label>
              <input
                type="number"
                value={settings.maxFileUploadSize}
                onChange={(e) => updateSetting('maxFileUploadSize', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                min="1"
                max="100"
              />
            </div>
          </div>
        </div>

        {/* Communication Settings */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
          <div className="flex items-center space-x-3 mb-6">
            <Mail className="h-6 w-6 text-green-600" />
            <h3 className="text-lg font-semibold text-slate-800">Communication</h3>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Default Welcome Message
              </label>
              <textarea
                value={settings.defaultWelcomeMessage}
                onChange={(e) => updateSetting('defaultWelcomeMessage', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
                placeholder="Hello! How can I help you today?"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Support Email Address
              </label>
              <input
                type="email"
                value={settings.supportEmail}
                onChange={(e) => updateSetting('supportEmail', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="<EMAIL>"
              />
            </div>
          </div>
        </div>

        {/* Security Settings */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
          <div className="flex items-center space-x-3 mb-6">
            <Shield className="h-6 w-6 text-red-600" />
            <h3 className="text-lg font-semibold text-slate-800">Security & Compliance</h3>
          </div>

          <div className="space-y-4">
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <AlertCircle className="h-5 w-5 text-amber-600 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-amber-900">Security Notice</h4>
                  <p className="text-sm text-amber-800 mt-1">
                    Security settings like rate limiting, API key rotation, and access controls
                    are managed through environment variables and system configuration.
                  </p>
                </div>
              </div>
            </div>

            <div className="text-sm text-slate-600 space-y-2">
              <p><strong>Current Security Features:</strong></p>
              <ul className="list-disc list-inside space-y-1 ml-4">
                <li>AES-256-GCM encryption for sensitive data</li>
                <li>JWT-based authentication with secure sessions</li>
                <li>Rate limiting on API endpoints</li>
                <li>CORS protection for public APIs</li>
                <li>Input validation and sanitization</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
