import { getServerSession } from "next-auth/next"
import { redirect } from "next/navigation"
import { authOptions } from "@/lib/auth"
import { DollarSign, TrendingUp, Users, CreditCard, AlertTriangle, CheckCircle, Calendar, RefreshCw } from "lucide-react"
import { prisma } from "@/lib/prisma"

async function getRevenueMetrics() {
  const now = new Date()
  const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1)
  const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
  const thisMonthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0)

  // Get subscription data with plans
  const [
    activeSubscriptions,
    totalRevenue,
    thisMonthRevenue,
    lastMonthRevenue,
    failedPayments,
    cancelledSubscriptions,
    subscriptionsByPlan,
    recentSubscriptions
  ] = await Promise.all([
    // Active subscriptions
    prisma.subscription.findMany({
      where: { status: 'active' },
      include: {
        user: { select: { name: true, email: true } },
        plan: { select: { name: true, price: true } }
      }
    }),

    // Total revenue (all active subscriptions)
    prisma.subscription.findMany({
      where: { status: 'active' },
      include: { plan: true }
    }).then(subs => subs.reduce((sum, sub) => sum + sub.plan.price, 0)),

    // This month's new revenue
    prisma.subscription.findMany({
      where: {
        status: 'active',
        createdAt: { gte: thisMonth, lte: thisMonthEnd }
      },
      include: { plan: true }
    }).then(subs => subs.reduce((sum, sub) => sum + sub.plan.price, 0)),

    // Last month's revenue
    prisma.subscription.findMany({
      where: {
        status: 'active',
        createdAt: { gte: lastMonth, lt: thisMonth }
      },
      include: { plan: true }
    }).then(subs => subs.reduce((sum, sub) => sum + sub.plan.price, 0)),

    // Failed/past due subscriptions
    prisma.subscription.count({
      where: { status: 'past_due' }
    }),

    // Cancelled subscriptions this month
    prisma.subscription.count({
      where: {
        status: 'cancelled',
        updatedAt: { gte: thisMonth }
      }
    }),

    // Subscriptions by plan
    prisma.subscription.groupBy({
      by: ['planId'],
      where: { status: 'active' },
      _count: { planId: true },
      _sum: { tokensUsedThisPeriod: true }
    }),

    // Recent subscriptions
    prisma.subscription.findMany({
      take: 10,
      orderBy: { createdAt: 'desc' },
      include: {
        user: { select: { name: true, email: true } },
        plan: { select: { name: true, price: true } }
      }
    })
  ])

  // Calculate growth
  const revenueGrowth = lastMonthRevenue > 0 
    ? Math.round(((thisMonthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100)
    : thisMonthRevenue > 0 ? 100 : 0

  // Get plan details for grouping
  const plans = await prisma.plan.findMany()
  const planMap = plans.reduce((acc, plan) => {
    acc[plan.id] = plan
    return acc
  }, {} as Record<string, any>)

  const planStats = subscriptionsByPlan.map(stat => ({
    plan: planMap[stat.planId],
    count: stat._count.planId,
    revenue: planMap[stat.planId]?.price * stat._count.planId || 0,
    tokensUsed: stat._sum.tokensUsedThisPeriod || 0
  }))

  return {
    activeSubscriptions: activeSubscriptions.length,
    totalRevenue,
    thisMonthRevenue,
    revenueGrowth,
    failedPayments,
    cancelledSubscriptions,
    planStats,
    recentSubscriptions,
    subscriptionDetails: activeSubscriptions
  }
}

export default async function AdminRevenuePage() {
  const session = await getServerSession(authOptions)

  if (!session || session.user.role !== "ADMIN") {
    redirect("/dashboard")
  }

  const metrics = await getRevenueMetrics()

  const revenueStats = [
    {
      title: "Monthly Recurring Revenue",
      value: `₹${(metrics.totalRevenue / 100).toLocaleString()}`,
      change: `${metrics.revenueGrowth >= 0 ? '+' : ''}${metrics.revenueGrowth}%`,
      positive: metrics.revenueGrowth >= 0,
      icon: DollarSign,
      color: "from-green-500 to-emerald-600"
    },
    {
      title: "Active Subscriptions",
      value: metrics.activeSubscriptions.toString(),
      change: `${metrics.thisMonthRevenue > 0 ? '+' : ''}${(metrics.thisMonthRevenue / 100).toLocaleString()} this month`,
      positive: true,
      icon: Users,
      color: "from-blue-500 to-indigo-600"
    },
    {
      title: "Failed Payments",
      value: metrics.failedPayments.toString(),
      change: "Requires attention",
      positive: metrics.failedPayments === 0,
      icon: AlertTriangle,
      color: "from-red-500 to-pink-600"
    },
    {
      title: "Churn This Month",
      value: metrics.cancelledSubscriptions.toString(),
      change: "Cancelled subscriptions",
      positive: metrics.cancelledSubscriptions === 0,
      icon: RefreshCw,
      color: "from-orange-500 to-red-600"
    }
  ]

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="relative overflow-hidden bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 rounded-2xl p-8 text-white">
        <div className="relative z-10">
          <div className="flex items-center space-x-3 mb-4">
            <DollarSign className="h-8 w-8 text-green-300" />
            <h1 className="text-4xl font-bold">Revenue & Billing</h1>
          </div>
          <p className="text-green-100 text-lg">
            Monitor subscription revenue, billing issues, and financial metrics
          </p>
        </div>
        <div className="absolute top-0 right-0 -mt-4 -mr-4 w-32 h-32 bg-white/10 rounded-full"></div>
      </div>

      {/* Revenue Stats */}
      <div>
        <h2 className="text-2xl font-bold text-slate-800 mb-6">Revenue Overview</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {revenueStats.map((stat, index) => (
            <div key={index} className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
              <div className="flex items-center justify-between mb-4">
                <div className={`inline-flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-r ${stat.color} text-white`}>
                  <stat.icon className="h-6 w-6" />
                </div>
                <TrendingUp className={`h-4 w-4 ${stat.positive ? 'text-green-500' : 'text-red-500'}`} />
              </div>
              <h3 className="text-lg font-semibold text-slate-800 mb-1">{stat.title}</h3>
              <p className="text-3xl font-bold text-slate-800 mb-2">{stat.value}</p>
              <p className={`text-sm font-medium ${stat.positive ? 'text-green-600' : 'text-red-600'}`}>
                {stat.change}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Plan Performance */}
      <div>
        <h2 className="text-2xl font-bold text-slate-800 mb-6">Plan Performance</h2>
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-slate-50">
                <tr>
                  <th className="text-left py-4 px-6 font-medium text-slate-700">Plan</th>
                  <th className="text-left py-4 px-6 font-medium text-slate-700">Subscribers</th>
                  <th className="text-left py-4 px-6 font-medium text-slate-700">Monthly Revenue</th>
                  <th className="text-left py-4 px-6 font-medium text-slate-700">Price</th>
                  <th className="text-left py-4 px-6 font-medium text-slate-700">Tokens Used</th>
                </tr>
              </thead>
              <tbody>
                {metrics.planStats.map((planStat, index) => (
                  <tr key={index} className="border-b border-gray-100">
                    <td className="py-4 px-6">
                      <div className="font-medium text-slate-800">{planStat.plan?.name || 'Unknown'}</div>
                    </td>
                    <td className="py-4 px-6 text-slate-600">
                      {planStat.count}
                    </td>
                    <td className="py-4 px-6">
                      <span className="font-semibold text-green-600">
                        ₹{(planStat.revenue / 100).toLocaleString()}
                      </span>
                    </td>
                    <td className="py-4 px-6 text-slate-600">
                      ₹{(planStat.plan?.price / 100).toLocaleString()}/month
                    </td>
                    <td className="py-4 px-6 text-slate-600">
                      {planStat.tokensUsed.toLocaleString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Recent Subscriptions */}
      <div>
        <h2 className="text-2xl font-bold text-slate-800 mb-6">Recent Subscriptions</h2>
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-slate-50">
                <tr>
                  <th className="text-left py-4 px-6 font-medium text-slate-700">User</th>
                  <th className="text-left py-4 px-6 font-medium text-slate-700">Plan</th>
                  <th className="text-left py-4 px-6 font-medium text-slate-700">Status</th>
                  <th className="text-left py-4 px-6 font-medium text-slate-700">Revenue</th>
                  <th className="text-left py-4 px-6 font-medium text-slate-700">Date</th>
                </tr>
              </thead>
              <tbody>
                {metrics.recentSubscriptions.map((subscription, index) => (
                  <tr key={index} className="border-b border-gray-100">
                    <td className="py-4 px-6">
                      <div>
                        <div className="font-medium text-slate-800">{subscription.user.name}</div>
                        <div className="text-sm text-slate-600">{subscription.user.email}</div>
                      </div>
                    </td>
                    <td className="py-4 px-6">
                      <span className="font-medium text-slate-800">{subscription.plan.name}</span>
                    </td>
                    <td className="py-4 px-6">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        subscription.status === 'active' 
                          ? 'bg-green-100 text-green-800'
                          : subscription.status === 'past_due'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {subscription.status === 'active' && <CheckCircle className="h-3 w-3 mr-1" />}
                        {subscription.status === 'past_due' && <AlertTriangle className="h-3 w-3 mr-1" />}
                        {subscription.status.charAt(0).toUpperCase() + subscription.status.slice(1)}
                      </span>
                    </td>
                    <td className="py-4 px-6">
                      <span className="font-semibold text-green-600">
                        ₹{(subscription.plan.price / 100).toLocaleString()}
                      </span>
                    </td>
                    <td className="py-4 px-6 text-slate-600">
                      {new Date(subscription.createdAt).toLocaleDateString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}
