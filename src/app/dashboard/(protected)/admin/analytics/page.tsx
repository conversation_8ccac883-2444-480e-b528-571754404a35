import { getServerSession } from "next-auth/next"
import { redirect } from "next/navigation"
import { authOptions } from "@/lib/auth"
import { TrendingUp, Users, Bot, MessageSquare, Calendar, Activity, DollarSign, Target } from "lucide-react"
import { prisma } from "@/lib/prisma"

async function getAnalyticsData() {
  const now = new Date()
  const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1)
  const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
  const last30Days = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
  const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)

  const [
    totalUsers,
    newUsersThisMonth,
    newUsersLastMonth,
    totalChatbots,
    activeChatbots,
    totalSessions,
    sessionsLast30Days,
    sessionsLast7Days,
    totalMessages,
    messagesLast30Days,
    activeSubscriptions,
    totalRevenue,
    topPlans,
    userGrowthData,
    sessionGrowthData
  ] = await Promise.all([
    // User metrics
    prisma.user.count(),
    prisma.user.count({ where: { createdAt: { gte: thisMonth } } }),
    prisma.user.count({ where: { createdAt: { gte: lastMonth, lt: thisMonth } } }),

    // Chatbot metrics
    prisma.chatbot.count(),
    prisma.chatbot.count({
      where: {
        chatSessions: {
          some: {
            createdAt: { gte: last30Days }
          }
        }
      }
    }),

    // Session metrics
    prisma.chatSession.count(),
    prisma.chatSession.count({ where: { createdAt: { gte: last30Days } } }),
    prisma.chatSession.count({ where: { createdAt: { gte: last7Days } } }),

    // Message metrics
    prisma.message.count(),
    prisma.message.count({ where: { createdAt: { gte: last30Days } } }),

    // Revenue metrics
    prisma.subscription.count({ where: { status: 'active' } }),
    prisma.subscription.findMany({
      where: { status: 'active' },
      include: { plan: true }
    }).then(subs => subs.reduce((sum, sub) => sum + sub.plan.price, 0)),

    // Top performing plans
    prisma.subscription.groupBy({
      by: ['planId'],
      where: { status: 'active' },
      _count: { planId: true },
      orderBy: { _count: { planId: 'desc' } },
      take: 3
    }),

    // User growth over last 6 months
    Promise.all(
      Array.from({ length: 6 }, (_, i) => {
        const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1)
        const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 0)
        return prisma.user.count({
          where: {
            createdAt: { gte: monthStart, lte: monthEnd }
          }
        }).then(count => ({
          month: monthStart.toLocaleDateString('en-US', { month: 'short' }),
          users: count
        }))
      })
    ).then(data => data.reverse()),

    // Session growth over last 30 days (weekly)
    Promise.all(
      Array.from({ length: 4 }, (_, i) => {
        const weekStart = new Date(now.getTime() - (i + 1) * 7 * 24 * 60 * 60 * 1000)
        const weekEnd = new Date(now.getTime() - i * 7 * 24 * 60 * 60 * 1000)
        return prisma.chatSession.count({
          where: {
            createdAt: { gte: weekStart, lt: weekEnd }
          }
        }).then(count => ({
          week: `Week ${4 - i}`,
          sessions: count
        }))
      })
    )
  ])

  // Calculate growth rates
  const userGrowth = newUsersLastMonth > 0 
    ? Math.round(((newUsersThisMonth - newUsersLastMonth) / newUsersLastMonth) * 100)
    : newUsersThisMonth > 0 ? 100 : 0

  const chatbotUtilization = totalChatbots > 0 
    ? Math.round((activeChatbots / totalChatbots) * 100)
    : 0

  // Get plan details for top plans
  const planDetails = await prisma.plan.findMany({
    where: {
      id: { in: topPlans.map(p => p.planId) }
    }
  })

  const topPlansWithDetails = topPlans.map(stat => {
    const plan = planDetails.find(p => p.id === stat.planId)
    return {
      name: plan?.name || 'Unknown',
      subscribers: stat._count.planId,
      revenue: (plan?.price || 0) * stat._count.planId
    }
  })

  return {
    overview: {
      totalUsers,
      userGrowth,
      totalChatbots,
      activeChatbots,
      chatbotUtilization,
      totalSessions,
      sessionsLast30Days,
      sessionsLast7Days,
      totalMessages,
      messagesLast30Days,
      activeSubscriptions,
      totalRevenue
    },
    growth: {
      userGrowthData,
      sessionGrowthData
    },
    plans: topPlansWithDetails
  }
}

export default async function AdminAnalyticsPage() {
  const session = await getServerSession(authOptions)

  if (!session || session.user.role !== "ADMIN") {
    redirect("/dashboard")
  }

  const analytics = await getAnalyticsData()

  const overviewStats = [
    {
      title: "Total Users",
      value: analytics.overview.totalUsers.toLocaleString(),
      change: `${analytics.overview.userGrowth >= 0 ? '+' : ''}${analytics.overview.userGrowth}%`,
      positive: analytics.overview.userGrowth >= 0,
      icon: Users,
      color: "from-blue-500 to-indigo-600"
    },
    {
      title: "Active Chatbots",
      value: `${analytics.overview.activeChatbots}/${analytics.overview.totalChatbots}`,
      change: `${analytics.overview.chatbotUtilization}% utilization`,
      positive: analytics.overview.chatbotUtilization > 50,
      icon: Bot,
      color: "from-purple-500 to-violet-600"
    },
    {
      title: "Chat Sessions",
      value: analytics.overview.sessionsLast30Days.toLocaleString(),
      change: `${analytics.overview.sessionsLast7Days} this week`,
      positive: true,
      icon: MessageSquare,
      color: "from-green-500 to-emerald-600"
    },
    {
      title: "Monthly Revenue",
      value: `₹${(analytics.overview.totalRevenue / 100).toLocaleString()}`,
      change: `${analytics.overview.activeSubscriptions} subscriptions`,
      positive: true,
      icon: DollarSign,
      color: "from-yellow-500 to-orange-600"
    }
  ]

  return (
    <div className="space-y-8">


      {/* Overview Stats */}
      <div>
        <h2 className="text-2xl font-bold text-slate-800 mb-6">Platform Overview</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {overviewStats.map((stat, index) => (
            <div key={index} className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
              <div className="flex items-center justify-between mb-4">
                <div className={`inline-flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-r ${stat.color} text-white`}>
                  <stat.icon className="h-6 w-6" />
                </div>
                <TrendingUp className={`h-4 w-4 ${stat.positive ? 'text-green-500' : 'text-red-500'}`} />
              </div>
              <h3 className="text-lg font-semibold text-slate-800 mb-1">{stat.title}</h3>
              <p className="text-3xl font-bold text-slate-800 mb-2">{stat.value}</p>
              <p className={`text-sm font-medium ${stat.positive ? 'text-green-600' : 'text-red-600'}`}>
                {stat.change}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Growth Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* User Growth */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-slate-800 mb-4">User Growth (6 Months)</h3>
          <div className="space-y-3">
            {analytics.growth.userGrowthData.map((data, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-sm text-slate-600">{data.month}</span>
                <div className="flex items-center space-x-3">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ 
                        width: `${Math.max(10, (data.users / Math.max(...analytics.growth.userGrowthData.map(d => d.users))) * 100)}%` 
                      }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-slate-800 w-8">{data.users}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Session Activity */}
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-slate-800 mb-4">Session Activity (4 Weeks)</h3>
          <div className="space-y-3">
            {analytics.growth.sessionGrowthData.map((data, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-sm text-slate-600">{data.week}</span>
                <div className="flex items-center space-x-3">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-green-600 h-2 rounded-full" 
                      style={{ 
                        width: `${Math.max(10, (data.sessions / Math.max(...analytics.growth.sessionGrowthData.map(d => d.sessions))) * 100)}%` 
                      }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium text-slate-800 w-12">{data.sessions}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Top Plans */}
      <div>
        <h2 className="text-2xl font-bold text-slate-800 mb-6">Top Performing Plans</h2>
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-slate-50">
                <tr>
                  <th className="text-left py-4 px-6 font-medium text-slate-700">Plan Name</th>
                  <th className="text-left py-4 px-6 font-medium text-slate-700">Subscribers</th>
                  <th className="text-left py-4 px-6 font-medium text-slate-700">Monthly Revenue</th>
                  <th className="text-left py-4 px-6 font-medium text-slate-700">Market Share</th>
                </tr>
              </thead>
              <tbody>
                {analytics.plans.map((plan, index) => {
                  const marketShare = analytics.overview.activeSubscriptions > 0 
                    ? Math.round((plan.subscribers / analytics.overview.activeSubscriptions) * 100)
                    : 0
                  
                  return (
                    <tr key={index} className="border-b border-gray-100">
                      <td className="py-4 px-6">
                        <div className="font-medium text-slate-800">{plan.name}</div>
                      </td>
                      <td className="py-4 px-6 text-slate-600">
                        {plan.subscribers}
                      </td>
                      <td className="py-4 px-6">
                        <span className="font-semibold text-green-600">
                          ₹{(plan.revenue / 100).toLocaleString()}
                        </span>
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center space-x-3">
                          <div className="w-20 bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-purple-600 h-2 rounded-full" 
                              style={{ width: `${marketShare}%` }}
                            ></div>
                          </div>
                          <span className="text-sm text-slate-600">{marketShare}%</span>
                        </div>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Key Metrics Summary */}
      <div className="bg-gradient-to-r from-slate-50 to-gray-50 rounded-2xl p-6 border border-gray-200">
        <h3 className="text-lg font-semibold text-slate-800 mb-4">Key Performance Indicators</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{analytics.overview.totalMessages.toLocaleString()}</div>
            <div className="text-sm text-slate-600">Total Messages</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{analytics.overview.messagesLast30Days.toLocaleString()}</div>
            <div className="text-sm text-slate-600">Messages (30d)</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{analytics.overview.chatbotUtilization}%</div>
            <div className="text-sm text-slate-600">Bot Utilization</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {analytics.overview.totalSessions > 0 
                ? Math.round(analytics.overview.totalMessages / analytics.overview.totalSessions)
                : 0
              }
            </div>
            <div className="text-sm text-slate-600">Avg Messages/Session</div>
          </div>
        </div>
      </div>
    </div>
  )
}
