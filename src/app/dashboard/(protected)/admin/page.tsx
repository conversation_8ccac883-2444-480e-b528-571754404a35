import { getServerSession } from "next-auth/next"
import { redirect } from "next/navigation"
import { authOptions } from "@/lib/auth"
import { Crown, Users, DollarSign, TrendingUp, Shield, Database, ArrowRight, Activity, AlertTriangle, CheckCircle, Settings } from "lucide-react"
import { prisma } from "@/lib/prisma"

async function getBusinessMetrics() {
  const now = new Date()
  const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate())
  const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1)

  // Get real business metrics
  const [
    totalUsers,
    activeSubscriptions,
    totalRevenue,
    newUsersThisMonth,
    newUsersLastMonth,
    newSubscriptionsThisMonth,
    newSubscriptionsLastMonth,
    totalChatbots,
    totalSessions,
    tokensUsedToday
  ] = await Promise.all([
    // Total users
    prisma.user.count(),

    // Active subscriptions
    prisma.subscription.count({
      where: { status: 'active' }
    }),

    // Total revenue (sum of all active subscription prices)
    prisma.subscription.findMany({
      where: { status: 'active' },
      include: { plan: true }
    }).then(subs => subs.reduce((sum, sub) => sum + sub.plan.price, 0)),

    // New users this month
    prisma.user.count({
      where: {
        createdAt: { gte: thisMonth }
      }
    }),

    // New users last month
    prisma.user.count({
      where: {
        createdAt: {
          gte: lastMonth,
          lt: thisMonth
        }
      }
    }),

    // New subscriptions this month (users with active subscriptions created this month)
    prisma.user.count({
      where: {
        createdAt: { gte: thisMonth },
        subscription: { status: 'active' }
      }
    }),

    // New subscriptions last month (users with active subscriptions created last month)
    prisma.user.count({
      where: {
        createdAt: {
          gte: lastMonth,
          lt: thisMonth
        },
        subscription: { status: 'active' }
      }
    }),

    // Total chatbots
    prisma.chatbot.count(),

    // Total chat sessions
    prisma.chatSession.count(),

    // Tokens used today
    prisma.subscription.aggregate({
      _sum: { tokensUsedThisPeriod: true },
      where: { status: 'active' }
    }).then(result => result._sum.tokensUsedThisPeriod || 0)
  ])

  // Calculate growth percentages
  const userGrowth = newUsersLastMonth > 0
    ? Math.round(((newUsersThisMonth - newUsersLastMonth) / newUsersLastMonth) * 100)
    : newUsersThisMonth > 0 ? 100 : 0

  const subscriptionGrowth = newSubscriptionsLastMonth > 0
    ? Math.round(((newSubscriptionsThisMonth - newSubscriptionsLastMonth) / newSubscriptionsLastMonth) * 100)
    : newSubscriptionsThisMonth > 0 ? 100 : 0

  return {
    totalUsers,
    activeSubscriptions,
    totalRevenue,
    userGrowth,
    subscriptionGrowth,
    totalChatbots,
    totalSessions,
    tokensUsedToday
  }
}

export default async function AdminDashboardPage() {
  const session = await getServerSession(authOptions)

  if (!session || session.user.role !== "ADMIN") {
    redirect("/dashboard")
  }

  const metrics = await getBusinessMetrics()

  const adminFeatures = [
    {
      title: "Revenue & Billing",
      description: "Monitor revenue, subscriptions, and billing issues",
      icon: DollarSign,
      href: "/dashboard/admin/revenue",
      gradient: "from-green-500 to-emerald-600",
      bgGradient: "from-green-50 to-emerald-50",
      stats: `₹${(metrics.totalRevenue / 100).toLocaleString()} MRR`,
      status: "Ready"
    },
    {
      title: "User Management",
      description: "Manage user accounts, subscriptions, and billing issues",
      icon: Users,
      href: "/dashboard/admin/users",
      gradient: "from-blue-500 to-indigo-600",
      bgGradient: "from-blue-50 to-indigo-50",
      stats: `${metrics.totalUsers} total users`,
      status: "Ready"
    },
    {
      title: "Subscription Plans",
      description: "Create and manage subscription plans and pricing",
      icon: Crown,
      href: "/dashboard/admin/plans",
      gradient: "from-yellow-500 to-orange-600",
      bgGradient: "from-yellow-50 to-orange-50",
      stats: "3 active plans",
      status: "Ready"
    },
    {
      title: "Analytics",
      description: "Platform usage analytics and growth metrics",
      icon: TrendingUp,
      href: "/dashboard/admin/analytics",
      gradient: "from-purple-500 to-violet-600",
      bgGradient: "from-purple-50 to-violet-50",
      stats: `${metrics.totalSessions} sessions`,
      status: "Ready"
    },
    {
      title: "System Health",
      description: "Monitor system status and performance",
      icon: Activity,
      href: "/dashboard/admin/system",
      gradient: "from-red-500 to-pink-600",
      bgGradient: "from-red-50 to-pink-50",
      stats: "All systems operational",
      status: "Ready"
    },
    {
      title: "Platform Settings",
      description: "Configure system-wide settings and features",
      icon: Settings,
      href: "/dashboard/admin/settings",
      gradient: "from-gray-500 to-slate-600",
      bgGradient: "from-gray-50 to-slate-50",
      stats: "Global configuration",
      status: "Ready"
    }
  ]

  const systemStats = [
    {
      label: "Monthly Recurring Revenue",
      value: `₹${(metrics.totalRevenue / 100).toLocaleString()}`,
      change: `${metrics.subscriptionGrowth >= 0 ? '+' : ''}${metrics.subscriptionGrowth}%`,
      positive: metrics.subscriptionGrowth >= 0,
      icon: DollarSign
    },
    {
      label: "Total Users",
      value: metrics.totalUsers.toLocaleString(),
      change: `${metrics.userGrowth >= 0 ? '+' : ''}${metrics.userGrowth}%`,
      positive: metrics.userGrowth >= 0,
      icon: Users
    },
    {
      label: "Active Subscriptions",
      value: metrics.activeSubscriptions.toLocaleString(),
      change: `${metrics.subscriptionGrowth >= 0 ? '+' : ''}${metrics.subscriptionGrowth}%`,
      positive: metrics.subscriptionGrowth >= 0,
      icon: Crown
    },
    {
      label: "Platform Activity",
      value: `${metrics.totalSessions.toLocaleString()} sessions`,
      change: `${metrics.tokensUsedToday.toLocaleString()} tokens today`,
      positive: true,
      icon: Activity
    }
  ]

  return (
    <div className="space-y-8">


      {/* System Stats */}
      <div>
        <h2 className="text-2xl font-bold text-slate-800 mb-6">System Overview</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {systemStats.map((stat, index) => (
            <div key={index} className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
              <div className="flex items-center justify-between mb-2">
                <p className="text-slate-600 text-sm font-medium">{stat.label}</p>
                <stat.icon className={`h-5 w-5 ${stat.positive ? 'text-green-500' : 'text-red-500'}`} />
              </div>
              <p className="text-3xl font-bold text-slate-800 mb-1">{stat.value}</p>
              <p className={`text-sm font-medium ${stat.positive ? 'text-green-600' : 'text-red-600'}`}>
                {stat.change} from last month
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Admin Features */}
      <div>
        <h2 className="text-2xl font-bold text-slate-800 mb-6">Admin Features</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {adminFeatures.map((feature) => (
            <a
              key={feature.title}
              href={feature.href}
              className={`group relative overflow-hidden bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 ${
                feature.status === 'Coming Soon' ? 'opacity-75 cursor-not-allowed' : ''
              }`}
            >
              <div className={`absolute inset-0 bg-gradient-to-br ${feature.bgGradient} opacity-50`}></div>
              <div className="relative p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className={`inline-flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-r ${feature.gradient} text-white`}>
                    <feature.icon className="h-6 w-6" />
                  </div>
                  <div className="flex flex-col items-end">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      feature.status === 'Ready'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {feature.status}
                    </span>
                    {feature.status === 'Ready' && (
                      <ArrowRight className="h-5 w-5 text-slate-400 group-hover:text-slate-600 group-hover:translate-x-1 transition-all duration-200 mt-2" />
                    )}
                  </div>
                </div>
                <h3 className="text-lg font-semibold text-slate-800 mb-2 group-hover:text-slate-900">
                  {feature.title}
                </h3>
                <p className="text-slate-600 text-sm mb-3">
                  {feature.description}
                </p>
                <span className="text-xs text-slate-500">{feature.stats}</span>
              </div>
            </a>
          ))}
        </div>
      </div>

      {/* Admin Guide */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-2xl p-6">
        <div className="flex items-start space-x-4">
          <div className="flex-shrink-0">
            <CheckCircle className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-blue-900 mb-2">
              Welcome to the New Admin Panel
            </h3>
            <p className="text-blue-800 text-sm mb-4">
              The admin panel has been completely restructured to match our SaaS business model. Here's what's changed:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-blue-900 mb-3">✅ What You Now Manage:</h4>
                <ul className="space-y-2 text-sm text-blue-800">
                  <li>• <strong>Revenue & Billing:</strong> Real MRR, subscription health, payment issues</li>
                  <li>• <strong>User Accounts:</strong> Account status, billing problems, usage monitoring</li>
                  <li>• <strong>Platform Analytics:</strong> Growth metrics, usage patterns, performance</li>
                  <li>• <strong>System Health:</strong> Platform status, performance monitoring</li>
                  <li>• <strong>Business Settings:</strong> Plans, pricing, platform configuration</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-blue-900 mb-3">🚫 What Users Now Manage:</h4>
                <ul className="space-y-2 text-sm text-blue-800">
                  <li>• <strong>Account Creation:</strong> Users sign up via /signup page</li>
                  <li>• <strong>Chatbot Management:</strong> Users create/configure their own bots</li>
                  <li>• <strong>Knowledge Base:</strong> Users manage their own content</li>
                  <li>• <strong>Subscriptions:</strong> Users upgrade/downgrade themselves</li>
                  <li>• <strong>Settings:</strong> Users control their own preferences</li>
                </ul>
              </div>
            </div>
            <div className="mt-4 p-4 bg-blue-100 rounded-lg">
              <p className="text-sm text-blue-900">
                <strong>Key Change:</strong> You're now managing a <em>platform</em>, not individual chatbots.
                This scales much better and reduces support overhead while giving users full autonomy.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div>
        <h2 className="text-2xl font-bold text-slate-800 mb-6">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
            <h3 className="text-lg font-semibold text-slate-800 mb-4">Common Tasks</h3>
            <div className="space-y-3">
              <a href="/dashboard/admin/revenue" className="block text-sm text-blue-600 hover:text-blue-800">
                → Check failed payments
              </a>
              <a href="/dashboard/admin/users" className="block text-sm text-blue-600 hover:text-blue-800">
                → Review user activity
              </a>
              <a href="/dashboard/admin/analytics" className="block text-sm text-blue-600 hover:text-blue-800">
                → View growth metrics
              </a>
              <a href="/dashboard/admin/system" className="block text-sm text-blue-600 hover:text-blue-800">
                → Monitor system health
              </a>
            </div>
          </div>

          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
            <h3 className="text-lg font-semibold text-slate-800 mb-4">Business Management</h3>
            <div className="space-y-3">
              <a href="/dashboard/admin/plans" className="block text-sm text-green-600 hover:text-green-800">
                → Manage subscription plans
              </a>
              <a href="/dashboard/admin/settings" className="block text-sm text-green-600 hover:text-green-800">
                → Configure platform settings
              </a>
              <a href="/dashboard/admin/revenue" className="block text-sm text-green-600 hover:text-green-800">
                → Review revenue reports
              </a>
            </div>
          </div>

          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
            <h3 className="text-lg font-semibold text-slate-800 mb-4">Support & Monitoring</h3>
            <div className="space-y-3">
              <div className="text-sm text-slate-600">
                → Users sign up at <code className="bg-slate-100 px-1 rounded">/signup</code>
              </div>
              <div className="text-sm text-slate-600">
                → Users manage chatbots in their dashboard
              </div>
              <div className="text-sm text-slate-600">
                → Monitor usage in Analytics section
              </div>
              <div className="text-sm text-slate-600">
                → Check system status for issues
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
