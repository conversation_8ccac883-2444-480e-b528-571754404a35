'use client'

import { useState, useEffect } from 'react'
import { Activity, Mail, CreditCard, Database, Users, Bot, AlertTriangle, CheckCircle, Clock, Send } from 'lucide-react'

interface SystemStatus {
  database: 'healthy' | 'warning' | 'error'
  email: 'healthy' | 'warning' | 'error'
  billing: 'healthy' | 'warning' | 'error'
  totalUsers: number
  activeChatbots: number
  totalSessions: number
  tokensUsedToday: number
}

export default function AdminSystemPage() {
  const [status, setStatus] = useState<SystemStatus | null>(null)
  const [loading, setLoading] = useState(true)
  const [testingEmail, setTestingEmail] = useState(false)
  const [emailTestResult, setEmailTestResult] = useState<string | null>(null)
  const [checkingUsage, setCheckingUsage] = useState(false)
  const [usageCheckResult, setUsageCheckResult] = useState<string | null>(null)

  useEffect(() => {
    fetchSystemStatus()
  }, [])

  const fetchSystemStatus = async () => {
    try {
      const response = await fetch('/api/admin/system-status')
      if (response.ok) {
        const data = await response.json()
        setStatus(data)
      }
    } catch (error) {
      console.error('Failed to fetch system status:', error)
    } finally {
      setLoading(false)
    }
  }

  const testEmailSystem = async () => {
    setTestingEmail(true)
    setEmailTestResult(null)

    try {
      const response = await fetch('/api/admin/test-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'test',
          email: '<EMAIL>',
          smtpConfig: {
            host: 'smtp.gmail.com',
            port: 587,
            secure: false,
            user: '<EMAIL>',
            password: 'test-password'
          }
        }),
      })

      const result = await response.json()
      setEmailTestResult(result.success ? 'Email test successful!' : 'Email test failed: ' + result.message)
    } catch (error) {
      setEmailTestResult('Email test failed: ' + (error instanceof Error ? error.message : 'Unknown error'))
    } finally {
      setTestingEmail(false)
    }
  }

  const checkTokenUsage = async () => {
    setCheckingUsage(true)
    setUsageCheckResult(null)

    try {
      const response = await fetch('/api/admin/check-usage', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'both'
        }),
      })

      const result = await response.json()
      setUsageCheckResult(result.success ? result.message : 'Usage check failed: ' + result.error)
    } catch (error) {
      setUsageCheckResult('Usage check failed: ' + (error instanceof Error ? error.message : 'Unknown error'))
    } finally {
      setCheckingUsage(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-100'
      case 'warning': return 'text-yellow-600 bg-yellow-100'
      case 'error': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="h-4 w-4" />
      case 'warning': return <AlertTriangle className="h-4 w-4" />
      case 'error': return <AlertTriangle className="h-4 w-4" />
      default: return <Clock className="h-4 w-4" />
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="relative overflow-hidden bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600 rounded-2xl p-8 text-white">
        <div className="relative z-10">
          <div className="flex items-center space-x-3 mb-4">
            <Activity className="h-8 w-8 text-indigo-300" />
            <h1 className="text-4xl font-bold">System Status</h1>
          </div>
          <p className="text-indigo-100 text-lg">
            Monitor system health and perform administrative tasks
          </p>
        </div>
        <div className="absolute top-0 right-0 -mt-4 -mr-4 w-32 h-32 bg-white/10 rounded-full"></div>
      </div>

      {/* System Health Status */}
      <div>
        <h2 className="text-2xl font-bold text-slate-800 mb-6">System Health</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <Database className="h-6 w-6 text-blue-600" />
                <h3 className="text-lg font-semibold text-slate-800">Database</h3>
              </div>
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(status?.database || 'healthy')}`}>
                {getStatusIcon(status?.database || 'healthy')}
                <span className="ml-1 capitalize">{status?.database || 'healthy'}</span>
              </span>
            </div>
            <p className="text-slate-600 text-sm">PostgreSQL with pgvector extension</p>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <Mail className="h-6 w-6 text-green-600" />
                <h3 className="text-lg font-semibold text-slate-800">Email System</h3>
              </div>
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(status?.email || 'warning')}`}>
                {getStatusIcon(status?.email || 'warning')}
                <span className="ml-1 capitalize">{status?.email || 'warning'}</span>
              </span>
            </div>
            <p className="text-slate-600 text-sm">SMTP configuration and email delivery</p>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <CreditCard className="h-6 w-6 text-purple-600" />
                <h3 className="text-lg font-semibold text-slate-800">Billing</h3>
              </div>
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(status?.billing || 'warning')}`}>
                {getStatusIcon(status?.billing || 'warning')}
                <span className="ml-1 capitalize">{status?.billing || 'warning'}</span>
              </span>
            </div>
            <p className="text-slate-600 text-sm">Razorpay integration and webhooks</p>
          </div>
        </div>
      </div>

      {/* System Statistics */}
      <div>
        <h2 className="text-2xl font-bold text-slate-800 mb-6">System Statistics</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <div className="flex items-center justify-between mb-2">
              <Users className="h-6 w-6 text-blue-600" />
              <span className="text-2xl font-bold text-slate-800">{status?.totalUsers || 0}</span>
            </div>
            <p className="text-slate-600 text-sm">Total Users</p>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <div className="flex items-center justify-between mb-2">
              <Bot className="h-6 w-6 text-green-600" />
              <span className="text-2xl font-bold text-slate-800">{status?.activeChatbots || 0}</span>
            </div>
            <p className="text-slate-600 text-sm">Active Chatbots</p>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <div className="flex items-center justify-between mb-2">
              <Activity className="h-6 w-6 text-purple-600" />
              <span className="text-2xl font-bold text-slate-800">{status?.totalSessions || 0}</span>
            </div>
            <p className="text-slate-600 text-sm">Chat Sessions</p>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <div className="flex items-center justify-between mb-2">
              <Send className="h-6 w-6 text-orange-600" />
              <span className="text-2xl font-bold text-slate-800">{status?.tokensUsedToday || 0}</span>
            </div>
            <p className="text-slate-600 text-sm">Tokens Used Today</p>
          </div>
        </div>
      </div>

      {/* Administrative Actions */}
      <div>
        <h2 className="text-2xl font-bold text-slate-800 mb-6">Administrative Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <h3 className="text-lg font-semibold text-slate-800 mb-4">Email System Test</h3>
            <p className="text-slate-600 text-sm mb-4">
              Test the email system configuration and delivery.
            </p>
            <button
              onClick={testEmailSystem}
              disabled={testingEmail}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {testingEmail ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Testing...</span>
                </>
              ) : (
                <>
                  <Mail className="h-4 w-4" />
                  <span>Test Email</span>
                </>
              )}
            </button>
            {emailTestResult && (
              <div className={`mt-3 p-3 rounded-lg text-sm ${
                emailTestResult.includes('successful') 
                  ? 'bg-green-50 text-green-800 border border-green-200' 
                  : 'bg-red-50 text-red-800 border border-red-200'
              }`}>
                {emailTestResult}
              </div>
            )}
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
            <h3 className="text-lg font-semibold text-slate-800 mb-4">Token Usage Check</h3>
            <p className="text-slate-600 text-sm mb-4">
              Check token usage and send warning emails to users approaching limits.
            </p>
            <button
              onClick={checkTokenUsage}
              disabled={checkingUsage}
              className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {checkingUsage ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Checking...</span>
                </>
              ) : (
                <>
                  <Activity className="h-4 w-4" />
                  <span>Check Usage</span>
                </>
              )}
            </button>
            {usageCheckResult && (
              <div className={`mt-3 p-3 rounded-lg text-sm ${
                usageCheckResult.includes('completed') 
                  ? 'bg-green-50 text-green-800 border border-green-200' 
                  : 'bg-red-50 text-red-800 border border-red-200'
              }`}>
                {usageCheckResult}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
