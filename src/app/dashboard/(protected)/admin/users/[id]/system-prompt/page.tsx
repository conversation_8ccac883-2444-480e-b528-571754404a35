import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { redirect, notFound } from "next/navigation"
import { prisma } from "@/lib/prisma"
import UserSystemPromptSettings from "@/components/UserSystemPromptSettings"

export default async function UserSystemPromptPage({ params }: { params: Promise<{ id: string }> }) {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.id) {
    redirect('/login')
  }

  // Check if user is admin
  const currentUser = await prisma.user.findUnique({
    where: { id: session.user.id },
    select: { role: true }
  })

  if (!currentUser || currentUser.role !== 'ADMIN') {
    redirect('/dashboard')
  }

  const { id } = await params

  // Fetch the user with all necessary data
  const user = await prisma.user.findUnique({
    where: { id },
    include: {
      subscription: {
        include: {
          plan: true
        }
      }
    }
  })

  if (!user) {
    notFound()
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="relative overflow-hidden bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 rounded-2xl p-8 text-white">
        <div className="relative z-10">
          <div className="flex items-center space-x-3 mb-4">
            <h1 className="text-4xl font-bold">System Prompt Settings</h1>
          </div>
          <p className="text-purple-100 text-lg">
            Manage system prompt permissions and settings for {user.name || user.email}
          </p>
        </div>
        <div className="absolute top-0 right-0 -mt-4 -mr-4 w-32 h-32 bg-white/10 rounded-full"></div>
      </div>

      {/* Settings Component */}
      <UserSystemPromptSettings user={user} />
    </div>
  )
}
