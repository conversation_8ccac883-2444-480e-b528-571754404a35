"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { 
  User, 
  CreditCard, 
  Bot, 
  MessageSquare, 
  Calendar, 
  Activity,
  Edit,
  Save,
  X,
  Crown,
  TrendingUp,
  Settings
} from "lucide-react"

interface UserDetailsClientProps {
  user: any
  plans: any[]
}

export function UserDetailsClient({ user, plans }: UserDetailsClientProps) {
  const router = useRouter()
  const [isEditing, setIsEditing] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  
  const [editData, setEditData] = useState({
    planId: user.subscription?.planId || "",
    tokensUsedThisPeriod: user.subscription?.tokensUsedThisPeriod || 0,
    status: user.subscription?.status || "active"
  })

  const handleSave = async () => {
    setLoading(true)
    setError("")
    
    try {
      const response = await fetch(`/api/admin/users/${user.id}/subscription`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editData),
      })

      if (!response.ok) {
        throw new Error('Failed to update user subscription')
      }

      setIsEditing(false)
      router.refresh()
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      case 'past_due':
        return 'bg-yellow-100 text-yellow-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const currentPlan = plans.find(p => p.id === (user.subscription?.planId || editData.planId))

  return (
    <div className="space-y-8">
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-2xl p-4">
          <div className="text-red-800">{error}</div>
        </div>
      )}

      {/* User Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* User Info Card */}
        <div className="lg:col-span-2 bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-slate-800">User Information</h2>
            <button
              onClick={() => setIsEditing(!isEditing)}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              {isEditing ? <X className="h-4 w-4" /> : <Edit className="h-4 w-4" />}
              <span>{isEditing ? 'Cancel' : 'Edit Plan'}</span>
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">Name</label>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
                  <span className="text-white font-semibold text-sm">
                    {(user.name || user.email).charAt(0).toUpperCase()}
                  </span>
                </div>
                <div>
                  <div className="font-medium text-slate-800">{user.name || 'No name'}</div>
                  <div className="text-sm text-slate-500">{user.email}</div>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">Role</label>
              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                user.role === 'ADMIN' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'
              }`}>
                {user.role}
              </span>
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">Member Since</label>
              <div className="text-slate-800">{formatDate(user.createdAt)}</div>
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">Last Updated</label>
              <div className="text-slate-800">{formatDate(user.updatedAt)}</div>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="space-y-4">
          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-600 text-sm font-medium">Chatbots</p>
                <p className="text-3xl font-bold text-slate-800">{user._count.chatbots}</p>
              </div>
              <Bot className="h-8 w-8 text-purple-600" />
            </div>
          </div>

          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-600 text-sm font-medium">Total Sessions</p>
                <p className="text-3xl font-bold text-slate-800">{user.stats.totalSessions}</p>
              </div>
              <MessageSquare className="h-8 w-8 text-green-600" />
            </div>
          </div>

          <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-600 text-sm font-medium">Total Messages</p>
                <p className="text-3xl font-bold text-slate-800">{user.stats.totalMessages}</p>
              </div>
              <Activity className="h-8 w-8 text-blue-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Subscription Details */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
        <h2 className="text-2xl font-bold text-slate-800 mb-6">Subscription & Plan Settings</h2>
        
        {user.subscription ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">Current Plan</label>
              {isEditing ? (
                <select
                  value={editData.planId}
                  onChange={(e) => setEditData({...editData, planId: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {plans.map((plan) => (
                    <option key={plan.id} value={plan.id}>
                      {plan.name} - ₹{(plan.price / 100).toLocaleString()}/month
                    </option>
                  ))}
                </select>
              ) : (
                <div>
                  <div className="font-medium text-slate-800">{currentPlan?.name}</div>
                  <div className="text-sm text-slate-500">₹{(currentPlan?.price / 100).toLocaleString()}/month</div>
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">Status</label>
              {isEditing ? (
                <select
                  value={editData.status}
                  onChange={(e) => setEditData({...editData, status: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="active">Active</option>
                  <option value="past_due">Past Due</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              ) : (
                <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(user.subscription.status)}`}>
                  {user.subscription.status}
                </span>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">Tokens Used</label>
              {isEditing ? (
                <input
                  type="number"
                  value={editData.tokensUsedThisPeriod}
                  onChange={(e) => setEditData({...editData, tokensUsedThisPeriod: parseInt(e.target.value) || 0})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              ) : (
                <div>
                  <div className="font-medium text-slate-800">{user.subscription.tokensUsedThisPeriod.toLocaleString()}</div>
                  <div className="text-sm text-slate-500">
                    of {currentPlan?.features?.tokenLimit?.toLocaleString() || 'unlimited'}
                  </div>
                </div>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">Period End</label>
              <div className="text-slate-800">{formatDate(user.subscription.currentPeriodEnd)}</div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <CreditCard className="h-12 w-12 text-slate-400 mx-auto mb-4" />
            <div className="text-slate-500 text-lg font-medium">No Active Subscription</div>
            <div className="text-slate-400 text-sm">User has not subscribed to any plan</div>
          </div>
        )}

        {isEditing && (
          <div className="mt-6 flex justify-end space-x-4">
            <button
              onClick={() => setIsEditing(false)}
              className="px-4 py-2 text-slate-600 hover:text-slate-800 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              disabled={loading}
              className="flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Save className="h-4 w-4" />
              )}
              <span>{loading ? 'Saving...' : 'Save Changes'}</span>
            </button>
          </div>
        )}
      </div>
    </div>
  )
}
