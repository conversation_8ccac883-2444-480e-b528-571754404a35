import { getServerSession } from "next-auth/next"
import { redirect, notFound } from "next/navigation"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { UserDetailsClient } from "./UserDetailsClient"

interface UserDetailsPageProps {
  params: Promise<{ id: string }>
}

async function getUserDetails(userId: string) {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: {
      subscription: {
        include: {
          plan: true
        }
      },
      chatbots: {
        include: {
          chatSessions: {
            take: 5,
            orderBy: { createdAt: 'desc' },
            include: {
              visitor: true,
              messages: {
                take: 1,
                orderBy: { createdAt: 'desc' }
              }
            }
          }
        }
      },
      _count: {
        select: {
          chatbots: true
        }
      }
    }
  })

  if (!user) {
    return null
  }

  // Get additional stats
  const [totalSessions, totalMessages, tokensUsedAllTime] = await Promise.all([
    prisma.chatSession.count({
      where: {
        chatbot: {
          userId: userId
        }
      }
    }),
    prisma.message.count({
      where: {
        chatSession: {
          chatbot: {
            userId: userId
          }
        }
      }
    }),
    prisma.subscription.findUnique({
      where: { userId: userId },
      select: { tokensUsedThisPeriod: true }
    })
  ])

  return {
    ...user,
    stats: {
      totalSessions,
      totalMessages,
      tokensUsedThisPeriod: tokensUsedAllTime?.tokensUsedThisPeriod || 0
    }
  }
}

async function getAllPlans() {
  return await prisma.plan.findMany({
    where: { isActive: true },
    orderBy: { price: 'asc' }
  })
}

export default async function UserDetailsPage({ params }: UserDetailsPageProps) {
  const session = await getServerSession(authOptions)
  const { id } = await params

  if (!session || session.user.role !== "ADMIN") {
    redirect("/dashboard")
  }

  const [user, plans] = await Promise.all([
    getUserDetails(id),
    getAllPlans()
  ])

  if (!user) {
    notFound()
  }

  return (
    <UserDetailsClient user={user} plans={plans} />
  )
}
