import React from 'react'

interface WelcomeEmailProps {
  userName: string
  planName: string
  loginUrl: string
}

export default function WelcomeEmail({ userName, planName, loginUrl }: WelcomeEmailProps) {
  return (
    <html>
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Welcome to YogaBot Live</title>
      </head>
      <body style={{ fontFamily: 'Arial, sans-serif', lineHeight: '1.6', color: '#333', margin: 0, padding: 0 }}>
        <div style={{ maxWidth: '600px', margin: '0 auto', padding: '20px' }}>
          {/* Header */}
          <div style={{ textAlign: 'center', marginBottom: '30px' }}>
            <h1 style={{ color: '#3B82F6', fontSize: '28px', marginBottom: '10px' }}>
              Welcome to YogaBot Live! 🧘‍♀️
            </h1>
            <p style={{ color: '#666', fontSize: '16px', margin: 0 }}>
              Your AI-powered chatbot platform for yoga schools
            </p>
          </div>

          {/* Main Content */}
          <div style={{ backgroundColor: '#f8fafc', padding: '30px', borderRadius: '12px', marginBottom: '30px' }}>
            <h2 style={{ color: '#1e293b', fontSize: '22px', marginBottom: '20px' }}>
              Hi {userName}! 👋
            </h2>
            
            <p style={{ marginBottom: '20px', fontSize: '16px' }}>
              Thank you for joining YogaBot Live! We're excited to help you transform how your yoga school 
              connects with students and prospects through intelligent AI conversations.
            </p>

            <div style={{ backgroundColor: '#white', padding: '20px', borderRadius: '8px', marginBottom: '20px', border: '1px solid #e2e8f0' }}>
              <h3 style={{ color: '#3B82F6', fontSize: '18px', marginBottom: '15px', margin: '0 0 15px 0' }}>
                Your Subscription Details
              </h3>
              <p style={{ margin: '0 0 10px 0', fontSize: '16px' }}>
                <strong>Plan:</strong> {planName}
              </p>
              <p style={{ margin: '0', fontSize: '16px' }}>
                <strong>Status:</strong> Active ✅
              </p>
            </div>

            <h3 style={{ color: '#1e293b', fontSize: '18px', marginBottom: '15px' }}>
              What's Next?
            </h3>
            
            <ol style={{ paddingLeft: '20px', marginBottom: '20px' }}>
              <li style={{ marginBottom: '10px', fontSize: '16px' }}>
                <strong>Set up your first chatbot</strong> - Create and configure your AI assistant
              </li>
              <li style={{ marginBottom: '10px', fontSize: '16px' }}>
                <strong>Build your knowledge base</strong> - Add information about your yoga school
              </li>
              <li style={{ marginBottom: '10px', fontSize: '16px' }}>
                <strong>Customize the widget</strong> - Match your brand colors and style
              </li>
              <li style={{ marginBottom: '10px', fontSize: '16px' }}>
                <strong>Embed on your website</strong> - Start engaging with visitors
              </li>
            </ol>

            <div style={{ textAlign: 'center', marginTop: '30px' }}>
              <a 
                href={loginUrl}
                style={{
                  backgroundColor: '#3B82F6',
                  color: 'white',
                  padding: '12px 30px',
                  textDecoration: 'none',
                  borderRadius: '8px',
                  fontSize: '16px',
                  fontWeight: 'bold',
                  display: 'inline-block'
                }}
              >
                Get Started Now
              </a>
            </div>
          </div>

          {/* Features Highlight */}
          <div style={{ marginBottom: '30px' }}>
            <h3 style={{ color: '#1e293b', fontSize: '20px', marginBottom: '20px', textAlign: 'center' }}>
              What You Can Do With YogaBot Live
            </h3>
            
            <div style={{ display: 'grid', gap: '15px' }}>
              <div style={{ backgroundColor: '#f1f5f9', padding: '15px', borderRadius: '8px' }}>
                <h4 style={{ color: '#3B82F6', margin: '0 0 8px 0', fontSize: '16px' }}>
                  🤖 Intelligent Conversations
                </h4>
                <p style={{ margin: 0, fontSize: '14px', color: '#64748b' }}>
                  AI-powered responses about your classes, teachers, and policies
                </p>
              </div>
              
              <div style={{ backgroundColor: '#f1f5f9', padding: '15px', borderRadius: '8px' }}>
                <h4 style={{ color: '#3B82F6', margin: '0 0 8px 0', fontSize: '16px' }}>
                  📊 Lead Generation
                </h4>
                <p style={{ margin: 0, fontSize: '14px', color: '#64748b' }}>
                  Capture visitor information and track engagement
                </p>
              </div>
              
              <div style={{ backgroundColor: '#f1f5f9', padding: '15px', borderRadius: '8px' }}>
                <h4 style={{ color: '#3B82F6', margin: '0 0 8px 0', fontSize: '16px' }}>
                  💬 Live Chat Takeover
                </h4>
                <p style={{ margin: 0, fontSize: '14px', color: '#64748b' }}>
                  Seamlessly switch from AI to human when needed
                </p>
              </div>
            </div>
          </div>

          {/* Support */}
          <div style={{ backgroundColor: '#fef3c7', padding: '20px', borderRadius: '8px', marginBottom: '30px' }}>
            <h3 style={{ color: '#92400e', fontSize: '18px', marginBottom: '10px', margin: '0 0 10px 0' }}>
              Need Help Getting Started?
            </h3>
            <p style={{ margin: '0 0 15px 0', fontSize: '14px', color: '#92400e' }}>
              Our team is here to help you succeed. Don't hesitate to reach out if you have any questions!
            </p>
            <p style={{ margin: 0, fontSize: '14px', color: '#92400e' }}>
              📧 Email: <EMAIL><br />
              💬 Live Chat: Available in your dashboard
            </p>
          </div>

          {/* Footer */}
          <div style={{ textAlign: 'center', color: '#64748b', fontSize: '12px', borderTop: '1px solid #e2e8f0', paddingTop: '20px' }}>
            <p style={{ margin: '0 0 10px 0' }}>
              This email was sent to you because you signed up for YogaBot Live.
            </p>
            <p style={{ margin: 0 }}>
              © 2025 YogaBot Live. All rights reserved.
            </p>
          </div>
        </div>
      </body>
    </html>
  )
}
