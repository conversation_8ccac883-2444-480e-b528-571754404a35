import React from 'react'

interface SubscriptionWarningEmailProps {
  userName: string
  tokensUsed: number
  tokenLimit: number
  percentageUsed: number
  planName: string
  dashboardUrl: string
}

export default function SubscriptionWarningEmail({ 
  userName, 
  tokensUsed, 
  tokenLimit, 
  percentageUsed, 
  planName,
  dashboardUrl 
}: SubscriptionWarningEmailProps) {
  return (
    <html>
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Token Usage Alert - YogaBot Live</title>
      </head>
      <body style={{ fontFamily: 'Arial, sans-serif', lineHeight: '1.6', color: '#333', margin: 0, padding: 0 }}>
        <div style={{ maxWidth: '600px', margin: '0 auto', padding: '20px' }}>
          {/* Header */}
          <div style={{ textAlign: 'center', marginBottom: '30px' }}>
            <h1 style={{ color: '#f59e0b', fontSize: '28px', marginBottom: '10px' }}>
              Token Usage Alert ⚠️
            </h1>
            <p style={{ color: '#666', fontSize: '16px', margin: 0 }}>
              Your YogaBot Live subscription usage update
            </p>
          </div>

          {/* Main Content */}
          <div style={{ backgroundColor: '#fef3c7', padding: '30px', borderRadius: '12px', marginBottom: '30px', border: '2px solid #f59e0b' }}>
            <h2 style={{ color: '#92400e', fontSize: '22px', marginBottom: '20px' }}>
              Hi {userName}! 👋
            </h2>
            
            <p style={{ marginBottom: '20px', fontSize: '16px', color: '#92400e' }}>
              We wanted to let you know that your chatbot has been quite active! You've used{' '}
              <strong>{percentageUsed}%</strong> of your monthly token allowance.
            </p>

            {/* Usage Stats */}
            <div style={{ backgroundColor: '#white', padding: '20px', borderRadius: '8px', marginBottom: '20px', border: '1px solid #f59e0b' }}>
              <h3 style={{ color: '#92400e', fontSize: '18px', marginBottom: '15px', margin: '0 0 15px 0' }}>
                Current Usage - {planName} Plan
              </h3>
              
              <div style={{ marginBottom: '15px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                  <span style={{ fontSize: '14px', color: '#92400e' }}>Tokens Used</span>
                  <span style={{ fontSize: '14px', fontWeight: 'bold', color: '#92400e' }}>
                    {tokensUsed.toLocaleString()} / {tokenLimit.toLocaleString()}
                  </span>
                </div>
                <div style={{ 
                  width: '100%', 
                  height: '8px', 
                  backgroundColor: '#fef3c7', 
                  borderRadius: '4px',
                  overflow: 'hidden'
                }}>
                  <div style={{ 
                    width: `${percentageUsed}%`, 
                    height: '100%', 
                    backgroundColor: percentageUsed >= 90 ? '#dc2626' : '#f59e0b',
                    borderRadius: '4px'
                  }}></div>
                </div>
              </div>

              <p style={{ margin: '0', fontSize: '14px', color: '#92400e' }}>
                <strong>Remaining:</strong> {(tokenLimit - tokensUsed).toLocaleString()} tokens
              </p>
            </div>

            <h3 style={{ color: '#92400e', fontSize: '18px', marginBottom: '15px' }}>
              What happens when you reach your limit?
            </h3>
            
            <ul style={{ paddingLeft: '20px', marginBottom: '20px', color: '#92400e' }}>
              <li style={{ marginBottom: '8px', fontSize: '14px' }}>
                Your chatbot will temporarily stop responding to new conversations
              </li>
              <li style={{ marginBottom: '8px', fontSize: '14px' }}>
                Existing conversations and dashboard access remain available
              </li>
              <li style={{ marginBottom: '8px', fontSize: '14px' }}>
                Your tokens will reset at the start of your next billing cycle
              </li>
            </ul>

            <div style={{ backgroundColor: '#f0f9ff', padding: '15px', borderRadius: '8px', marginBottom: '20px', border: '1px solid #0ea5e9' }}>
              <h4 style={{ color: '#0369a1', margin: '0 0 8px 0', fontSize: '16px' }}>
                💡 Pro Tip
              </h4>
              <p style={{ margin: 0, fontSize: '14px', color: '#0369a1' }}>
                Consider upgrading to a higher plan for more tokens and advanced features, 
                or optimize your knowledge base to provide more efficient responses.
              </p>
            </div>

            <div style={{ textAlign: 'center', marginTop: '25px' }}>
              <a 
                href={dashboardUrl}
                style={{
                  backgroundColor: '#f59e0b',
                  color: 'white',
                  padding: '12px 30px',
                  textDecoration: 'none',
                  borderRadius: '8px',
                  fontSize: '16px',
                  fontWeight: 'bold',
                  display: 'inline-block',
                  marginRight: '10px'
                }}
              >
                View Dashboard
              </a>
              <a 
                href={`${dashboardUrl}/settings`}
                style={{
                  backgroundColor: '#0ea5e9',
                  color: 'white',
                  padding: '12px 30px',
                  textDecoration: 'none',
                  borderRadius: '8px',
                  fontSize: '16px',
                  fontWeight: 'bold',
                  display: 'inline-block'
                }}
              >
                Upgrade Plan
              </a>
            </div>
          </div>

          {/* Optimization Tips */}
          <div style={{ marginBottom: '30px' }}>
            <h3 style={{ color: '#1e293b', fontSize: '20px', marginBottom: '20px', textAlign: 'center' }}>
              Ways to Optimize Token Usage
            </h3>
            
            <div style={{ display: 'grid', gap: '15px' }}>
              <div style={{ backgroundColor: '#f1f5f9', padding: '15px', borderRadius: '8px' }}>
                <h4 style={{ color: '#3B82F6', margin: '0 0 8px 0', fontSize: '16px' }}>
                  📝 Refine Your Knowledge Base
                </h4>
                <p style={{ margin: 0, fontSize: '14px', color: '#64748b' }}>
                  More comprehensive information leads to more accurate, efficient responses
                </p>
              </div>
              
              <div style={{ backgroundColor: '#f1f5f9', padding: '15px', borderRadius: '8px' }}>
                <h4 style={{ color: '#3B82F6', margin: '0 0 8px 0', fontSize: '16px' }}>
                  🎯 Use Structured Data
                </h4>
                <p style={{ margin: 0, fontSize: '14px', color: '#64748b' }}>
                  Structured knowledge bases are more token-efficient than simple text
                </p>
              </div>
              
              <div style={{ backgroundColor: '#f1f5f9', padding: '15px', borderRadius: '8px' }}>
                <h4 style={{ color: '#3B82F6', margin: '0 0 8px 0', fontSize: '16px' }}>
                  💬 Monitor Conversations
                </h4>
                <p style={{ margin: 0, fontSize: '14px', color: '#64748b' }}>
                  Use the live chat feature to take over complex conversations
                </p>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div style={{ textAlign: 'center', color: '#64748b', fontSize: '12px', borderTop: '1px solid #e2e8f0', paddingTop: '20px' }}>
            <p style={{ margin: '0 0 10px 0' }}>
              This is an automated usage alert from YogaBot Live.
            </p>
            <p style={{ margin: 0 }}>
              © 2025 YogaBot Live. All rights reserved.
            </p>
          </div>
        </div>
      </body>
    </html>
  )
}
