{"name": "yogabot", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:seed": "tsx prisma/seed.ts"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.10.0", "@radix-ui/react-slot": "^1.2.3", "@types/nodemailer": "^6.4.17", "@types/uuid": "^10.0.0", "ably": "^2.9.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.516.0", "next": "15.3.3", "next-auth": "^4.24.11", "nodemailer": "^6.10.1", "razorpay": "^2.9.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-email": "^4.0.16", "resend": "^4.6.0", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "prisma": "^6.9.0", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}}