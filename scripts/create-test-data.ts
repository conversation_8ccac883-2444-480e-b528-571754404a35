import { prisma } from '../src/lib/prisma'

async function createTestData() {
  console.log('🧪 Creating test data for Sprint 3...')

  try {
    // Get the test user
    const testUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (!testUser) {
      console.error('❌ Test user not found. Please run the seed script first.')
      return
    }

    // Get the Basic plan
    const basicPlan = await prisma.plan.findUnique({
      where: { name: 'Basic' }
    })

    if (!basicPlan) {
      console.error('❌ Basic plan not found. Please run the seed script first.')
      return
    }

    // Create subscription for test user
    const subscription = await prisma.subscription.upsert({
      where: { userId: testUser.id },
      update: {},
      create: {
        userId: testUser.id,
        planId: basicPlan.id,
        status: 'active',
        currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        tokensUsedThisPeriod: 0,
        sessionsThisPeriod: 0
      }
    })

    console.log('✅ Created subscription for test user')

    // Check if chatbot already exists
    let chatbot = await prisma.chatbot.findFirst({
      where: {
        userId: testUser.id,
        approvedDomain: 'http://localhost:3000'
      }
    })

    if (!chatbot) {
      // Create a test chatbot
      chatbot = await prisma.chatbot.create({
        data: {
          userId: testUser.id,
          approvedDomain: 'http://localhost:3000',
          simpleKbText: `Welcome to Serenity Yoga Studio!

About Us:
Serenity Yoga Studio is a peaceful sanctuary dedicated to helping you find balance, strength, and inner peace through the practice of yoga. Founded in 2020, we offer a variety of yoga styles suitable for all levels.

Our Classes:
- Hatha Yoga: Perfect for beginners, focusing on basic postures and breathing
- Vinyasa Flow: Dynamic sequences linking breath with movement
- Yin Yoga: Slow, meditative practice with long-held poses
- Restorative Yoga: Gentle, relaxing practice using props for support

Schedule:
Monday - Friday: 6:00 AM - 9:00 PM
Saturday - Sunday: 8:00 AM - 6:00 PM

Pricing:
- Drop-in class: $20
- 10-class package: $180
- Monthly unlimited: $120
- New student special: 3 classes for $45

Our Teachers:
- Sarah Johnson: Lead instructor with 500-hour certification
- Mike Chen: Specializes in Vinyasa and Power Yoga
- Emma Davis: Yin and Restorative Yoga expert

Contact:
Phone: (*************
Email: <EMAIL>
Address: 123 Peaceful Lane, Wellness City, WC 12345

We also offer private sessions, workshops, and teacher training programs. Please contact us for more information!`,
          widgetConfig: {
            primaryColor: '#10B981',
            welcomeMessage: 'Welcome to Serenity Yoga Studio! How can I help you today?',
            placeholder: 'Ask about our classes, schedule, or pricing...',
            position: 'bottom-right',
            theme: 'light'
          }
        }
      })
    }

    console.log('✅ Created test chatbot with ID:', chatbot.id)

    // Create a persona for the chatbot
    const existingPersona = await prisma.persona.findFirst({
      where: {
        chatbotId: chatbot.id,
        name: 'Friendly Yoga Assistant'
      }
    })

    if (!existingPersona) {
      await prisma.persona.create({
        data: {
          chatbotId: chatbot.id,
          name: 'Friendly Yoga Assistant',
          personaText: 'You are a warm, welcoming, and knowledgeable yoga assistant. You speak with enthusiasm about yoga and wellness, always encouraging people to try new practices. You are patient with beginners and supportive of all students regardless of their level.',
          isActive: true
        }
      })
    }

    console.log('✅ Created persona for chatbot')

    console.log('')
    console.log('🎉 Test data created successfully!')
    console.log('')
    console.log('Test Details:')
    console.log(`- User: ${testUser.email}`)
    console.log(`- Plan: ${basicPlan.name}`)
    console.log(`- Chatbot ID: ${chatbot.id}`)
    console.log(`- Approved Domain: ${chatbot.approvedDomain}`)
    console.log('')
    console.log('Test the chat widget at:')
    console.log(`http://localhost:3000/widget/${chatbot.id}`)

  } catch (error) {
    console.error('❌ Error creating test data:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createTestData()
